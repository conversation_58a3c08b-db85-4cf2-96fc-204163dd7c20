using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;
using TYFManagementSystem.Services;
using Microsoft.Win32;
using System.IO;
using System.Text.Json;
using System.Text;

namespace TYFManagementSystem.Views.IPTT
{
    public partial class IPTTDesignWindow : Window
    {
        private Project currentProject;
        private ObservableCollection<IpttIndicator> ipttData;
        private ObservableCollection<IpttDisplayRow> displayRows;
        private List<string> monthColumns;
        private IpttDatabaseService databaseService;
        private int locationCount = 1; // عدد المواقع الافتراضي
        private Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData;
        private Dictionary<int, string> locationNames;
        private int currentLocationId = 1; // الموقع المختار حالياً

        public IPTTDesignWindow(Project project)
        {
            InitializeComponent();

            // التحقق من صحة المشروع
            if (project == null)
            {
                MessageBox.Show("خطأ: لم يتم تمرير مشروع صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close();
                return;
            }

            currentProject = project;

            // تهيئة المجموعات الأساسية
            locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();
            locationNames = new Dictionary<int, string>();

            // تهيئة المتغيرات الأساسية
            locationCount = 1; // قيمة افتراضية
            ipttData = new ObservableCollection<IpttIndicator>();
            displayRows = new ObservableCollection<IpttDisplayRow>();
            monthColumns = new List<string>();

            // تهيئة خدمة قاعدة البيانات
            databaseService = new IpttDatabaseService();

            InitializeWindow();

            // اختبار اتصال قاعدة البيانات
            TestDatabaseConnectionAsync();

            // تحميل البيانات من قاعدة البيانات إن وجدت، ثم إنشاء الجدول
            LoadIpttFromDatabaseAsync();

            // إضافة معالج إغلاق النافذة للحفظ التلقائي
            this.Closing += IPTTDesignWindow_Closing;
        }

        private void InitializeWindow()
        {
            ProjectNameText.Text = currentProject.Name;
            ProjectTitle.Text = currentProject.Name;
            ProjectManager.Text = currentProject.Manager;
            ProjectLocations.Text = string.IsNullOrWhiteSpace(currentProject.Region)
                                  ? "غير محدد"
                                  : currentProject.Region;

            var duration = (currentProject.EndDate - currentProject.StartDate).Days;
            var months = Math.Ceiling(duration / 30.0);
            ProjectDuration.Text = $"{months:F0} شهر من {currentProject.StartDate:yyyy/MM/dd} إلى {currentProject.EndDate:yyyy/MM/dd}";

            // تهيئة القائمة المنسدلة للمواقع
            InitializeLocationComboBox();

            // لا ننشئ بيانات فارغة هنا - سيتم تحميلها من قاعدة البيانات أو إنشاؤها لاحقاً
            System.Diagnostics.Debug.WriteLine("✅ تم تهيئة معلومات النافذة بدون مسح البيانات");
        }

        /// <summary>
        /// تهيئة القائمة المنسدلة للمواقع
        /// </summary>
        private void InitializeLocationComboBox()
        {
            try
            {
                LocationSelectionComboBox.Items.Clear();

                // إضافة المواقع إلى القائمة المنسدلة
                for (int i = 1; i <= locationCount; i++)
                {
                    string locationName = locationNames.ContainsKey(i) ? locationNames[i] : $"الموقع {i}";
                    LocationSelectionComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = locationName,
                        Tag = i
                    });
                }

                // إضافة تبويب الإجمالي إذا كان هناك أكثر من موقع واحد
                if (locationCount > 1)
                {
                    LocationSelectionComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = "الإجمالي",
                        Tag = 0
                    });
                }

                // اختيار الموقع الأول افتراضياً
                if (LocationSelectionComboBox.Items.Count > 0)
                {
                    LocationSelectionComboBox.SelectedIndex = 0;
                    currentLocationId = 1;
                    UpdateCurrentLocationDisplay();
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تهيئة القائمة المنسدلة مع {LocationSelectionComboBox.Items.Count} موقع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة القائمة المنسدلة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عرض الموقع الحالي
        /// </summary>
        private void UpdateCurrentLocationDisplay()
        {
            try
            {
                string locationName = currentLocationId == 0 ? "الإجمالي" :
                                    (locationNames.ContainsKey(currentLocationId) ? locationNames[currentLocationId] : $"الموقع {currentLocationId}");

                CurrentLocationText.Text = $"الموقع الحالي: {locationName}";

                // عرض معلومات إضافية عن البيانات
                if (locationData.ContainsKey(currentLocationId))
                {
                    var dataRows = locationData[currentLocationId].Where(r => r.IsDataTypeRow).Count();
                    var filledRows = locationData[currentLocationId].Where(r => r.IsDataTypeRow &&
                        r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();

                    LocationDataStatusText.Text = $"({dataRows} مؤشر، {filledRows} يحتوي على بيانات)";
                }
                else
                {
                    LocationDataStatusText.Text = "(لا توجد بيانات)";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث عرض الموقع: {ex.Message}");
            }
        }

        private void GenerateIpttTable()
        {
            try
            {
                // Generate month columns based on project duration
                GenerateMonthColumns();

                // Create initial display rows
                CreateDisplayRows();

                // Setup DataGrid columns
                SetupDataGridColumns();

                // Bind data to grid
                IpttDataGrid.ItemsSource = displayRows;

                // إضافة معالج الأحداث للحساب التلقائي
                SetupMainDataGridEventHandlers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء جدول IPTT: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupMainDataGridEventHandlers()
        {
            // استخدام نفس منطق معالجة الأحداث
            SetupDataGridEventHandlers(IpttDataGrid, 1);
        }

        private void GenerateMonthColumns()
        {
            monthColumns = new List<string>();
            var startDate = currentProject.StartDate;
            var endDate = currentProject.EndDate;

            var current = new DateTime(startDate.Year, startDate.Month, 1);
            while (current <= endDate)
            {
                monthColumns.Add(current.ToString("yyyy/MM", CultureInfo.InvariantCulture));
                current = current.AddMonths(1);
            }
        }

        private void CreateDisplayRows()
        {
            displayRows.Clear();

            foreach (var indicator in ipttData)
            {
                // Add main indicator row
                var mainRow = new IpttDisplayRow
                {
                    No = indicator.No,
                    Indicator = indicator.Indicator,
                    DataType = "",
                    Target = "",
                    Achievement = "",
                    AchievementPercentage = "",
                    IsMainIndicator = true,
                    CanDelete = true,
                    IndicatorId = indicator.No,
                    MonthlyData = new Dictionary<string, string>()
                };

                foreach (var month in monthColumns)
                {
                    mainRow.MonthlyData[month] = "";
                }

                displayRows.Add(mainRow);

                // Add data type rows
                foreach (var dataType in indicator.DataTypes)
                {
                    var dataTypeRow = new IpttDisplayRow
                    {
                        No = "",
                        Indicator = "",
                        DataType = dataType.Name,
                        Target = dataType.Target,
                        Achievement = "",
                        AchievementPercentage = "",
                        IsDataTypeRow = true,
                        IndicatorId = indicator.No,
                        MonthlyData = new Dictionary<string, string>()
                    };

                    // تهيئة جميع الأشهر بقيم فارغة
                    foreach (var month in monthColumns)
                    {
                        // استخدام القيمة الموجودة إذا كانت متوفرة، وإلا استخدام قيمة فارغة
                        if (dataType.MonthlyValues != null && dataType.MonthlyValues.ContainsKey(month))
                        {
                            dataTypeRow.MonthlyData[month] = dataType.MonthlyValues[month];
                        }
                        else
                        {
                            dataTypeRow.MonthlyData[month] = "";
                        }
                    }

                    displayRows.Add(dataTypeRow);
                }
            }
        }

        private void SetupDataGridColumns()
        {
            IpttDataGrid.Columns.Clear();

            // Fixed columns
            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "رقم",
                Binding = new Binding("No"),
                Width = new DataGridLength(60),
                IsReadOnly = true
            });

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "المؤشر",
                Binding = new Binding("Indicator"),
                Width = new DataGridLength(200),
                IsReadOnly = true
            });

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "نوع البيانات",
                Binding = new Binding("DataType"),
                Width = new DataGridLength(120),
                IsReadOnly = true
            });

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الهدف",
                Binding = new Binding("Target"),
                Width = new DataGridLength(80),
                IsReadOnly = false
            });

            // Monthly columns
            foreach (var month in monthColumns)
            {
                var monthColumn = new DataGridTextColumn
                {
                    Header = month,
                    Binding = new Binding($"MonthlyData[{month}]"),
                    Width = new DataGridLength(80),
                    IsReadOnly = false
                };

                // إضافة Style لجعل الصفوف الرئيسية غير قابلة للتحرير
                var style = new Style(typeof(DataGridCell));
                var trigger = new DataTrigger();
                trigger.Binding = new Binding("IsMainIndicator");
                trigger.Value = true;
                trigger.Setters.Add(new Setter(DataGridCell.IsEnabledProperty, false));
                trigger.Setters.Add(new Setter(DataGridCell.BackgroundProperty, System.Windows.Media.Brushes.LightGray));
                style.Triggers.Add(trigger);
                monthColumn.CellStyle = style;

                IpttDataGrid.Columns.Add(monthColumn);
            }

            // Achievement columns
            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الإنجاز %",
                Binding = new Binding("AchievementPercentage"),
                Width = new DataGridLength(80),
                IsReadOnly = true
            });

            // Delete button column
            var deleteColumn = new DataGridTemplateColumn
            {
                Header = "حذف",
                Width = new DataGridLength(60)
            };

            var deleteTemplate = new DataTemplate();
            var buttonFactory = new FrameworkElementFactory(typeof(Button));
            buttonFactory.SetValue(Button.ContentProperty, "🗑️");
            buttonFactory.SetValue(Button.BackgroundProperty, System.Windows.Media.Brushes.Red);
            buttonFactory.SetValue(Button.ForegroundProperty, System.Windows.Media.Brushes.White);
            buttonFactory.SetValue(Button.BorderThicknessProperty, new Thickness(0));
            buttonFactory.SetValue(Button.CursorProperty, System.Windows.Input.Cursors.Hand);
            buttonFactory.SetValue(Button.VisibilityProperty, new Binding("CanDelete")
            {
                Converter = new BooleanToVisibilityConverter()
            });
            buttonFactory.AddHandler(Button.ClickEvent, new RoutedEventHandler(DeleteIndicator_Click));

            deleteTemplate.VisualTree = buttonFactory;
            deleteColumn.CellTemplate = deleteTemplate;
            IpttDataGrid.Columns.Add(deleteColumn);
        }

        private void AddIndicatorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create a simple dialog for adding indicators
                var dialog = new AddIndicatorDialog();
                dialog.Owner = this;

                if (dialog.ShowDialog() == true)
                {
                    var newIndicator = dialog.GetIndicator();
                    if (newIndicator != null)
                    {
                        ipttData.Add(newIndicator);
                        CreateDisplayRows();
                        SetupDataGridColumns();
                        IpttDataGrid.ItemsSource = displayRows;

                        // إعادة إعداد معالج الأحداث
                        SetupMainDataGridEventHandlers();

                        // حساب الإنجاز للبيانات الجديدة
                        CalculateAchievementForLocation(1);

                        // حفظ تلقائي بعد إضافة المؤشر
                        AutoSaveDataAsync();

                        MessageBox.Show("تم إضافة المؤشر بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المؤشر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteIndicator_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var row = button?.DataContext as IpttDisplayRow;

            if (row != null && !string.IsNullOrEmpty(row.IndicatorId))
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المؤشر '{row.Indicator}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف المؤشر من البيانات الأساسية
                    var indicatorToRemove = ipttData.FirstOrDefault(i => i.No == row.IndicatorId);
                    if (indicatorToRemove != null)
                    {
                        ipttData.Remove(indicatorToRemove);

                        // إعادة ترقيم المؤشرات
                        RenumberIndicators();

                        // تحديث العرض
                        CreateDisplayRows();
                        IpttDataGrid.ItemsSource = displayRows;

                        MessageBox.Show("تم حذف المؤشر بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
        }

        private void RenumberIndicators()
        {
            for (int i = 0; i < ipttData.Count; i++)
            {
                ipttData[i].No = (i + 1).ToString();
            }
        }

        private void LocationCountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // يمكن إضافة تحقق من صحة الإدخال هنا إذا لزم الأمر
        }

        /// <summary>
        /// معالج تغيير اختيار الموقع من القائمة المنسدلة
        /// </summary>
        private void LocationSelectionComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (LocationSelectionComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    int selectedLocationId = (int)selectedItem.Tag;

                    // حفظ البيانات الحالية قبل التبديل
                    SaveCurrentLocationData();

                    // تبديل إلى الموقع الجديد
                    currentLocationId = selectedLocationId;

                    // تحميل بيانات الموقع الجديد
                    LoadLocationData(currentLocationId);

                    // تحديث عرض الموقع الحالي
                    UpdateCurrentLocationDisplay();

                    System.Diagnostics.Debug.WriteLine($"✅ تم التبديل إلى الموقع {currentLocationId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تغيير الموقع: {ex.Message}");
                MessageBox.Show($"خطأ في تغيير الموقع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyLocationCountButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود مؤشرات مدخلة أولاً
                if (displayRows == null || displayRows.Count == 0)
                {
                    MessageBox.Show("يرجى إدخال المؤشرات أولاً قبل تحديد عدد المواقع!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (int.TryParse(LocationCountTextBox.Text, out int newCount) && newCount > 0 && newCount <= 20)
                {
                    // حفظ البيانات الحالية قبل إعادة الإنشاء
                    SaveCurrentLocationData();

                    locationCount = newCount;

                    // إنشاء بيانات المواقع الجديدة
                    CreateLocationData();

                    // تحديث القائمة المنسدلة
                    InitializeLocationComboBox();

                    // تحديث رؤية زر الحفظ
                    UpdateSaveAllLocationsButtonVisibility();

                    MessageBox.Show($"تم إنشاء {locationCount} موقع بنجاح!\nتم استنساخ نفس المؤشرات والتنسيقات لجميع المواقع.", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("يرجى إدخال رقم صحيح بين 1 و 20", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    LocationCountTextBox.Text = locationCount.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ بيانات الموقع الحالي
        /// </summary>
        private void SaveCurrentLocationData()
        {
            try
            {
                if (IpttDataGrid.ItemsSource is ObservableCollection<IpttDisplayRow> currentRows)
                {
                    // إنهاء أي تحرير جاري
                    IpttDataGrid.CommitEdit();
                    IpttDataGrid.CommitEdit(DataGridEditingUnit.Row, true);

                    // حفظ البيانات في المجموعة المناسبة
                    locationData[currentLocationId] = new ObservableCollection<IpttDisplayRow>(currentRows);

                    // تحديث displayRows إذا كان الموقع الحالي هو الموقع الأول
                    if (currentLocationId == 1)
                    {
                        displayRows = new ObservableCollection<IpttDisplayRow>(currentRows);
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ بيانات الموقع {currentLocationId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ بيانات الموقع: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات موقع معين
        /// </summary>
        private void LoadLocationData(int locationId)
        {
            try
            {
                if (locationData.ContainsKey(locationId))
                {
                    // تحميل البيانات الموجودة
                    IpttDataGrid.ItemsSource = locationData[locationId];

                    // إعداد معالجات الأحداث
                    SetupMainDataGridEventHandlers();

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل بيانات الموقع {locationId}");
                }
                else
                {
                    // إنشاء بيانات فارغة للموقع الجديد
                    CreateLocationDisplayRows(locationId);
                    IpttDataGrid.ItemsSource = locationData[locationId];
                    SetupMainDataGridEventHandlers();

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء بيانات جديدة للموقع {locationId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الموقع: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء بيانات المواقع
        /// </summary>
        private void CreateLocationData()
        {
            try
            {
                // الحفاظ على البيانات الموجودة للمواقع الحالية
                var existingData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>(locationData);

                // إعادة تهيئة بيانات المواقع
                locationData.Clear();
                locationNames.Clear();

                // إنشاء بيانات لكل موقع
                for (int i = 1; i <= locationCount; i++)
                {
                    locationNames[i] = $"الموقع {i}";

                    if (existingData.ContainsKey(i))
                    {
                        // استخدام البيانات الموجودة
                        locationData[i] = existingData[i];
                    }
                    else
                    {
                        // إنشاء بيانات جديدة بناءً على displayRows
                        CreateLocationDisplayRows(i);
                    }
                }

                // إنشاء تبويب الإجمالي إذا كان هناك أكثر من موقع واحد
                if (locationCount > 1)
                {
                    locationNames[0] = "الإجمالي";
                    if (existingData.ContainsKey(0))
                    {
                        locationData[0] = existingData[0];
                    }
                    else
                    {
                        locationData[0] = CreateTotalDisplayRows();
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء بيانات {locationCount} موقع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء بيانات المواقع: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صفوف العرض لموقع معين
        /// </summary>
        private void CreateLocationDisplayRows(int locationId)
        {
            try
            {
                var locationRows = new ObservableCollection<IpttDisplayRow>();

                if (displayRows != null)
                {
                    foreach (var row in displayRows)
                    {
                        var newRow = new IpttDisplayRow
                        {
                            No = row.No,
                            Indicator = row.Indicator,
                            DataType = row.DataType,
                            Target = row.Target,
                            Achievement = "",
                            AchievementPercentage = "",
                            IsMainIndicator = row.IsMainIndicator,
                            IsDataTypeRow = row.IsDataTypeRow,
                            IsTotalRow = false,
                            IsCompleted = false,
                            CanDelete = row.CanDelete,
                            IndicatorId = row.IndicatorId,
                            MonthlyData = new Dictionary<string, string>()
                        };

                        // تهيئة البيانات الشهرية
                        foreach (var month in monthColumns)
                        {
                            newRow.MonthlyData[month] = "";
                        }

                        locationRows.Add(newRow);
                    }
                }

                locationData[locationId] = locationRows;
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {locationRows.Count} صف للموقع {locationId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفوف الموقع {locationId}: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صفوف الإجمالي
        /// </summary>
        // تم إزالة الدالة القديمة CreateTotalDisplayRows واستبدالها بالدالة الجديدة التي ترجع قيمة

        // تم إزالة دالة RemoveAdditionalTabs لأنها لم تعد مطلوبة مع النظام الجديد

        // تم إزالة دالة CreateLocationTabs لأنها لم تعد مطلوبة مع النظام الجديد

        /// <summary>
        /// إنشاء نسخة عميقة من البيانات للمواقع الجديدة
        /// </summary>
        private ObservableCollection<IpttDisplayRow> CreateDeepCopyOfDisplayRows(ObservableCollection<IpttDisplayRow> sourceRows)
        {
            var copiedRows = new ObservableCollection<IpttDisplayRow>();

            foreach (var sourceRow in sourceRows)
            {
                var newRow = new IpttDisplayRow
                {
                    No = sourceRow.No,
                    Indicator = sourceRow.Indicator,
                    DataType = sourceRow.DataType,
                    Target = sourceRow.Target, // نسخ الهدف
                    Achievement = "", // إعادة تعيين الإنجاز
                    AchievementPercentage = "", // إعادة تعيين النسبة
                    IsMainIndicator = sourceRow.IsMainIndicator,
                    IsDataTypeRow = sourceRow.IsDataTypeRow,
                    IsTotalRow = sourceRow.IsTotalRow,
                    IsCompleted = false,
                    CanDelete = sourceRow.CanDelete,
                    IndicatorId = sourceRow.IndicatorId,
                    MonthlyData = new Dictionary<string, string>()
                };

                // نسخ البيانات الشهرية (فارغة للمواقع الجديدة)
                foreach (var monthData in sourceRow.MonthlyData)
                {
                    newRow.MonthlyData[monthData.Key] = "";
                }

                copiedRows.Add(newRow);
            }

            return copiedRows;
        }

        /// <summary>
        /// إعداد معالجات الأحداث لجميع الجداول
        /// </summary>
        private void SetupAllDataGridEventHandlers()
        {
            try
            {
                // إعداد معالج الأحداث للجدول الرئيسي
                SetupDataGridEventHandlers(IpttDataGrid, 1);

                // إعداد معالجات الأحداث لجميع جداول المواقع
                foreach (var kvp in locationDataGrids.Where(x => x.Key > 1))
                {
                    SetupDataGridEventHandlers(kvp.Value, kvp.Key);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إعداد معالجات الأحداث لـ {locationDataGrids.Count} جدول");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد معالجات الأحداث: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب الإنجاز لجميع المواقع
        /// </summary>
        private void CalculateAllLocationsAchievement()
        {
            try
            {
                for (int i = 1; i <= locationCount; i++)
                {
                    CalculateAchievementForLocation(i);
                }

                // تحديث تبويب الإجمالي إذا كان موجوداً
                if (locationCount > 1)
                {
                    UpdateTotalTab();
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم حساب الإنجاز لـ {locationCount} موقع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حساب الإنجاز: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عرض جميع الجداول
        /// </summary>
        /// <summary>
        /// تحديث عرض الجدول الحالي (النظام الجديد)
        /// </summary>
        private void RefreshAllDataGrids()
        {
            try
            {
                // تحديث الجدول الحالي فقط
                IpttDataGrid.Items.Refresh();
                UpdateCurrentLocationDisplay();

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث عرض الجدول الحالي");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الجدول: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث جميع الحسابات لجميع المواقع (النظام الجديد)
        /// </summary>
        private void RefreshAllCalculations()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحديث جميع الحسابات");

                // حفظ البيانات الحالية أولاً
                SaveCurrentLocationData();

                // تحديث حسابات جميع المواقع
                for (int i = 1; i <= locationCount; i++)
                {
                    if (locationData.ContainsKey(i))
                    {
                        System.Diagnostics.Debug.WriteLine($"🧮 حساب الإنجاز للموقع {i}");
                        CalculateAchievementForLocationData(locationData[i]);
                    }
                }

                // تحديث الإجمالي
                if (locationCount > 1 && locationData.ContainsKey(0))
                {
                    System.Diagnostics.Debug.WriteLine("📊 تحديث الإجمالي");
                    UpdateTotalFromAllLocations();
                }

                // تحديث عرض الجدول الحالي
                IpttDataGrid.Items.Refresh();
                UpdateCurrentLocationDisplay();

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث جميع الحسابات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الحسابات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تحميل البيانات بالقوة من قاعدة البيانات
        /// </summary>
        private async void ForceReloadDataFromDatabase()
        {
            try
            {
                if (currentProject == null) return;

                System.Diagnostics.Debug.WriteLine("🔄 إعادة تحميل البيانات بالقوة من قاعدة البيانات");

                // تحميل البيانات من قاعدة البيانات مرة أخرى
                var loadResult = await databaseService.LoadIpttDataAsync(currentProject.Name);

                if (loadResult != null)
                {
                    // تحديث البيانات المحلية
                    ipttData = loadResult.Indicators;
                    monthColumns = loadResult.MonthColumns;
                    locationData = loadResult.LocationData;
                    locationCount = loadResult.LocationCount;

                    System.Diagnostics.Debug.WriteLine($"🔄 تم إعادة تحميل: {ipttData?.Count ?? 0} مؤشر، {locationCount} موقع");

                    // تحديث القائمة المنسدلة والعرض
                    InitializeLocationComboBox();

                    // تحديث الحسابات
                    RefreshAllCalculations();

                    System.Diagnostics.Debug.WriteLine("✅ تم إعادة التحميل بالقوة بنجاح");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة التحميل بالقوة: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف جميع التبويبات الحالية
        /// </summary>
        private void ClearAllTabs()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 تنظيف جميع التبويبات");

                // تنظيف التبويبات
                Dispatcher.Invoke(() => {
                    // الاحتفاظ بالتبويب الرئيسي فقط
                    while (LocationTabControl.Items.Count > 1)
                    {
                        LocationTabControl.Items.RemoveAt(LocationTabControl.Items.Count - 1);
                    }
                });

                // تنظيف قاموس الجداول
                locationDataGrids.Clear();
                locationDataGrids[1] = IpttDataGrid; // إعادة إضافة الجدول الرئيسي

                System.Diagnostics.Debug.WriteLine("✅ تم تنظيف التبويبات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف التبويبات: {ex.Message}");
            }
        }

        /// <summary>
        /// ربط البيانات بالقوة لجميع التبويبات
        /// </summary>
        private async Task ForceDataBinding()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔗 بدء ربط البيانات بالقوة");

                await Task.Delay(200); // انتظار لضمان إنشاء التبويبات

                // ربط البيانات لكل موقع
                foreach (var locationKvp in locationData)
                {
                    int locationIndex = locationKvp.Key;
                    var rows = locationKvp.Value;

                    System.Diagnostics.Debug.WriteLine($"🔗 ربط البيانات للموقع {locationIndex} ({rows.Count} صف)");

                    if (locationIndex == 1)
                    {
                        // الموقع الرئيسي
                        Dispatcher.Invoke(() => {
                            displayRows.Clear();
                            foreach (var row in rows)
                            {
                                displayRows.Add(row);
                            }

                            IpttDataGrid.ItemsSource = null;
                            IpttDataGrid.ItemsSource = displayRows;
                            IpttDataGrid.UpdateLayout();
                            IpttDataGrid.Items.Refresh();
                        });
                    }
                    else if (locationDataGrids.ContainsKey(locationIndex))
                    {
                        // المواقع الأخرى
                        var dataGrid = locationDataGrids[locationIndex];
                        Dispatcher.Invoke(() => {
                            dataGrid.ItemsSource = null;
                            dataGrid.ItemsSource = rows;
                            SetupLocationDataGridColumns(dataGrid);
                            SetupDataGridEventHandlers(dataGrid, locationIndex);
                            dataGrid.UpdateLayout();
                            dataGrid.Items.Refresh();
                        });
                    }
                }

                await Task.Delay(200); // انتظار إضافي

                System.Diagnostics.Debug.WriteLine("✅ تم ربط البيانات بالقوة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في ربط البيانات بالقوة: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة إعدادات الجداول وإصلاحها تلقائياً
        /// </summary>
        private void ValidateAndFixDataGridSettings()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 بدء التحقق من إعدادات الجداول...");

                // التحقق من الجدول الرئيسي
                if (IpttDataGrid != null)
                {
                    IpttDataGrid.IsReadOnly = false;
                    System.Diagnostics.Debug.WriteLine("✅ تم تفعيل التحرير للجدول الرئيسي");
                }

                // التحقق من جميع جداول المواقع
                foreach (var kvp in locationDataGrids)
                {
                    var dataGrid = kvp.Value;
                    if (dataGrid != null)
                    {
                        dataGrid.IsReadOnly = false;
                        dataGrid.CanUserAddRows = false;
                        dataGrid.CanUserDeleteRows = false;
                        System.Diagnostics.Debug.WriteLine($"✅ تم تفعيل التحرير للموقع {kvp.Key}");
                    }
                }

                System.Diagnostics.Debug.WriteLine("🎉 تم التحقق من جميع إعدادات الجداول وإصلاحها");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من إعدادات الجداول: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح أهداف تبويب الإجمالي - نسخ نفس الهدف من المواقع (وليس الجمع)
        /// </summary>
        private void FixTotalTabTargets()
        {
            try
            {
                if (locationCount <= 1 || !locationData.ContainsKey(0))
                {
                    return; // لا يوجد تبويب إجمالي
                }

                System.Diagnostics.Debug.WriteLine("🔧 بدء إصلاح أهداف تبويب الإجمالي - نسخ نفس الهدف...");

                var totalRows = locationData[0];

                foreach (var totalRow in totalRows)
                {
                    // نسخ نفس الهدف من الموقع الأول (وليس الجمع)
                    // مثال: إذا كان الهدف في جميع المواقع = 44، فالهدف في الإجمالي = 44 (وليس 44×4=176)
                    if (locationData.ContainsKey(1))
                    {
                        var location1Rows = locationData[1];
                        var matchingRow = location1Rows.FirstOrDefault(r =>
                            r.IndicatorId == totalRow.IndicatorId &&
                            r.DataType == totalRow.DataType &&
                            r.IsDataTypeRow == totalRow.IsDataTypeRow);

                        if (matchingRow != null && !string.IsNullOrWhiteSpace(matchingRow.Target))
                        {
                            // نسخ نفس الهدف بالضبط (44 = 44)
                            totalRow.Target = matchingRow.Target;
                            System.Diagnostics.Debug.WriteLine($"✅ تم نسخ هدف '{totalRow.Indicator} - {totalRow.DataType}' = {matchingRow.Target} (نفس القيمة)");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("🎉 تم نسخ جميع أهداف تبويب الإجمالي بنفس القيم");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح أهداف تبويب الإجمالي: {ex.Message}");
            }
        }



        /// <summary>
        /// ربط البيانات الموجودة بالجداول بعد التحميل
        /// </summary>
        private void ConnectExistingDataToGrids()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"بدء ربط البيانات لـ {locationData.Count} موقع");

                // ربط البيانات بالجداول
                foreach (var locationKvp in locationData)
                {
                    int locationIndex = locationKvp.Key;
                    var rows = locationKvp.Value;

                    System.Diagnostics.Debug.WriteLine($"معالجة الموقع {locationIndex} مع {rows.Count} صف");

                    if (locationIndex == 1)
                    {
                        // الموقع الرئيسي - ربط البيانات المحملة
                        displayRows.Clear();
                        foreach (var row in rows)
                        {
                            displayRows.Add(row);
                        }

                        // إعداد الأعمدة للجدول الرئيسي
                        SetupDataGridColumns();

                        IpttDataGrid.ItemsSource = displayRows;
                        locationDataGrids[1] = IpttDataGrid;
                        SetupDataGridEventHandlers(IpttDataGrid, 1);

                        System.Diagnostics.Debug.WriteLine($"تم ربط {rows.Count} صف للموقع الرئيسي");
                    }
                    else if (locationIndex > 1)
                    {
                        // المواقع الأخرى - التحقق من وجود DataGrid أولاً
                        if (locationDataGrids.ContainsKey(locationIndex))
                        {
                            var dataGrid = locationDataGrids[locationIndex];

                            // إعداد الأعمدة للموقع
                            SetupLocationDataGridColumns(dataGrid);

                            // ربط البيانات المحملة
                            dataGrid.ItemsSource = rows;
                            SetupDataGridEventHandlers(dataGrid, locationIndex);

                            System.Diagnostics.Debug.WriteLine($"تم ربط {rows.Count} صف للموقع {locationIndex}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"تأجيل ربط الموقع {locationIndex} - DataGrid غير موجود بعد");
                        }
                    }
                    else if (locationIndex == 0)
                    {
                        // تبويب الإجمالي - سيتم إنشاؤه تلقائياً
                        System.Diagnostics.Debug.WriteLine($"تم العثور على بيانات الإجمالي مع {rows.Count} صف");
                    }
                }

                // تحديث عرض البيانات
                IpttDataGrid.Items.Refresh();
                foreach (var dataGrid in locationDataGrids.Values)
                {
                    dataGrid.Items.Refresh();
                }

                System.Diagnostics.Debug.WriteLine($"تم ربط البيانات لـ {locationData.Count} موقع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ربط البيانات الموجودة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        // تم إزالة دالة EnsureAllDataGridsConnected لأنها لم تعد مطلوبة مع النظام الجديد

        // تم إزالة دالة UpdateMainTabHeader لأنها لم تعد مطلوبة مع النظام الجديد



        // تم إزالة دالة CreateLocationTab لأنها لم تعد مطلوبة مع النظام الجديد

        private async void SaveIpttButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("💾 بدء الحفظ اليدوي");

                // إنهاء أي تحرير جاري في الجداول
                CommitAllDataGridEdits();

                // جمع البيانات من جميع الجداول المفتوحة
                CollectDataFromAllGrids();

                // تحديث البيانات الأساسية من صفوف العرض
                UpdateUnderlyingDataFromDisplayRows();

                // تحديث الحسابات قبل الحفظ
                RefreshAllCalculations();

                // حفظ في قاعدة البيانات
                await SaveIpttToDatabaseAsync();

                // حفظ نسخة احتياطية في ملف JSON
                SaveIpttToJsonFile();

                // رسالة نجاح مع تفاصيل
                var totalSavedData = 0;
                if (locationData != null)
                {
                    foreach (var location in locationData.Values)
                    {
                        totalSavedData += location.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                    }
                }

                var message = locationCount > 1
                    ? $"تم حفظ بيانات جميع المواقع بنجاح!\n\nالتفاصيل:\n- عدد المواقع: {locationCount}\n- إجمالي البيانات المحفوظة: {totalSavedData}"
                    : "تم حفظ بيانات IPTT في قاعدة البيانات بنجاح!";

                MessageBox.Show(message, "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine("✅ تم الحفظ اليدوي بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);

                // تسجيل تفاصيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في الحفظ: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// إنهاء جميع عمليات التحرير الجارية في الجداول
        /// </summary>
        /// <summary>
        /// إنهاء عمليات التحرير الجارية في الجدول الحالي (النظام الجديد)
        /// </summary>
        private void CommitAllDataGridEdits()
        {
            try
            {
                // إنهاء التحرير في الجدول الحالي فقط
                IpttDataGrid.CommitEdit(DataGridEditingUnit.Row, true);
                IpttDataGrid.CommitEdit(DataGridEditingUnit.Cell, true);

                System.Diagnostics.Debug.WriteLine("✅ تم إنهاء التحرير في الجدول الحالي");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنهاء التحرير: {ex.Message}");
            }
        }



        private void TestCalculations()
        {
            try
            {
                RefreshAllCalculations();
                System.Diagnostics.Debug.WriteLine("تم اختبار الحسابات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في اختبار الحسابات: {ex.Message}");
                throw;
            }
        }

        private void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set EPPlus license context
                OfficeOpenXml.ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"IPTT_{currentProject.Name}_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportToExcel(saveFileDialog.FileName);

                    var result = MessageBox.Show("تم تصدير IPTT إلى Excel بنجاح!\n\nهل تريد فتح الملف؟",
                                               "نجح التصدير",
                                               MessageBoxButton.YesNo,
                                               MessageBoxImage.Information);

                    if (result == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveFileDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصدير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ البيانات الحالية قبل التحديث
                SaveCurrentLocationData();

                // تحديث الحسابات لجميع المواقع
                for (int i = 1; i <= locationCount; i++)
                {
                    if (locationData.ContainsKey(i))
                    {
                        CalculateAchievementForLocationData(locationData[i]);
                    }
                }

                // تحديث الإجمالي إذا كان موجوداً
                if (locationCount > 1 && locationData.ContainsKey(0))
                {
                    UpdateTotalFromAllLocations();
                }

                // تحديث عرض البيانات الحالي
                IpttDataGrid.Items.Refresh();

                // تحديث عرض الموقع الحالي
                UpdateCurrentLocationDisplay();

                // إظهار ملخص النتائج
                ShowCalculationSummary();

                MessageBox.Show("تم تحديث الحسابات بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حساب الإنجاز لبيانات موقع معين
        /// </summary>
        private void CalculateAchievementForLocationData(ObservableCollection<IpttDisplayRow> locationRows)
        {
            try
            {
                if (locationRows == null || monthColumns == null) return;

                foreach (var row in locationRows.Where(r => r.IsDataTypeRow))
                {
                    // حساب إجمالي الإنجاز من البيانات الشهرية
                    double totalAchievement = 0;
                    foreach (var monthData in row.MonthlyData)
                    {
                        if (double.TryParse(monthData.Value, out double value))
                        {
                            totalAchievement += value;
                        }
                    }

                    // تحديث الإنجاز
                    row.Achievement = totalAchievement.ToString("F0");

                    // حساب نسبة الإنجاز
                    if (double.TryParse(row.Target, out double target) && target > 0)
                    {
                        double percentage = (totalAchievement / target) * 100;
                        row.AchievementPercentage = $"{percentage:F1}%";
                    }
                    else
                    {
                        row.AchievementPercentage = "0%";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حساب الإنجاز: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإجمالي من جميع المواقع
        /// </summary>
        private void UpdateTotalFromAllLocations()
        {
            try
            {
                if (!locationData.ContainsKey(0) || locationCount <= 1) return;

                var totalRows = locationData[0];

                foreach (var totalRow in totalRows.Where(r => r.IsDataTypeRow))
                {
                    // إعادة تعيين البيانات الشهرية
                    foreach (var month in monthColumns)
                    {
                        double monthTotal = 0;

                        // جمع القيم من جميع المواقع
                        for (int i = 1; i <= locationCount; i++)
                        {
                            if (locationData.ContainsKey(i))
                            {
                                var locationRow = locationData[i].FirstOrDefault(r =>
                                    r.IsDataTypeRow && r.IndicatorId == totalRow.IndicatorId && r.DataType == totalRow.DataType);

                                if (locationRow != null && locationRow.MonthlyData.ContainsKey(month))
                                {
                                    if (double.TryParse(locationRow.MonthlyData[month], out double value))
                                    {
                                        monthTotal += value;
                                    }
                                }
                            }
                        }

                        totalRow.MonthlyData[month] = monthTotal.ToString("F0");
                    }
                }

                // حساب الإنجاز للإجمالي
                CalculateAchievementForLocationData(totalRows);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الإجمالي: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// معالج زر "حفظ البيانات لجميع المواقع" - يحفظ بيانات جميع المواقع في قاعدة البيانات
        /// </summary>
        private async void SaveAllLocationsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (currentProject == null)
                {
                    MessageBox.Show("لا يوجد مشروع محدد!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine("💾 بدء حفظ البيانات لجميع المواقع");

                // حفظ البيانات الحالية من الجدول المعروض
                SaveCurrentLocationData();

                // تحديث الحسابات لجميع المواقع
                for (int i = 1; i <= locationCount; i++)
                {
                    if (locationData.ContainsKey(i))
                    {
                        CalculateAchievementForLocationData(locationData[i]);
                    }
                }

                // تحديث الإجمالي إذا كان موجوداً
                if (locationCount > 1 && locationData.ContainsKey(0))
                {
                    UpdateTotalFromAllLocations();
                }

                // تحديث البيانات الأساسية
                UpdateUnderlyingDataFromDisplayRows();

                // حفظ في قاعدة البيانات
                await SaveIpttToDatabaseAsync();

                // حفظ نسخة احتياطية في ملف JSON
                SaveIpttToJsonFile();

                // رسالة نجاح
                var totalSavedData = 0;
                if (locationData != null)
                {
                    foreach (var location in locationData.Values)
                    {
                        totalSavedData += location.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                    }
                }

                MessageBox.Show($"تم حفظ بيانات جميع المواقع بنجاح!\n\nالتفاصيل:\n- عدد المواقع: {locationCount}\n- إجمالي البيانات المحفوظة: {totalSavedData}",
                              "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ البيانات لجميع المواقع بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فحص البيانات في قاعدة البيانات - للتشخيص
        /// </summary>
        private async void CheckDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (currentProject == null)
                {
                    MessageBox.Show("لا يوجد مشروع محدد!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine("🔍 بدء فحص البيانات في قاعدة البيانات");

                // تحميل البيانات من قاعدة البيانات
                var loadResult = await databaseService.LoadIpttDataAsync(currentProject.Id.ToString());

                var report = new System.Text.StringBuilder();
                report.AppendLine("📊 تقرير فحص البيانات");
                report.AppendLine("=" + new string('=', 30));
                report.AppendLine($"🏢 المشروع: {currentProject.Name}");
                report.AppendLine($"🆔 معرف المشروع: {currentProject.Id}");
                report.AppendLine();

                if (loadResult != null)
                {
                    report.AppendLine("✅ تم العثور على بيانات في قاعدة البيانات:");
                    report.AppendLine($"   - عدد المؤشرات: {loadResult.Indicators?.Count ?? 0}");
                    report.AppendLine($"   - عدد المواقع: {loadResult.LocationCount}");
                    report.AppendLine($"   - بيانات المواقع: {loadResult.LocationData?.Count ?? 0}");
                    report.AppendLine();

                    if (loadResult.LocationData != null && loadResult.LocationData.Count > 0)
                    {
                        report.AppendLine("📍 تفاصيل بيانات المواقع:");
                        foreach (var location in loadResult.LocationData.OrderBy(l => l.Key))
                        {
                            var dataRowsWithValues = location.Value.Where(r => r.IsDataTypeRow &&
                                r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();

                            report.AppendLine($"   🏠 الموقع {location.Key}:");
                            report.AppendLine($"      - إجمالي الصفوف: {location.Value.Count}");
                            report.AppendLine($"      - صفوف تحتوي على بيانات: {dataRowsWithValues}");

                            // عرض عينة من البيانات
                            var sampleData = location.Value.Where(r => r.IsDataTypeRow &&
                                r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0"))
                                .Take(3);

                            foreach (var row in sampleData)
                            {
                                var nonEmptyData = row.MonthlyData.Where(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0").Take(2);
                                foreach (var data in nonEmptyData)
                                {
                                    report.AppendLine($"         • {row.IndicatorId}-{row.DataType}: {data.Key} = {data.Value}");
                                }
                            }
                        }
                    }
                    else
                    {
                        report.AppendLine("⚠️ لا توجد بيانات مواقع في قاعدة البيانات");
                    }
                }
                else
                {
                    report.AppendLine("❌ لا توجد بيانات في قاعدة البيانات لهذا المشروع");
                }

                // فحص البيانات المحلية أيضاً
                report.AppendLine();
                report.AppendLine("💻 البيانات المحلية الحالية:");
                report.AppendLine($"   - عدد المؤشرات: {ipttData?.Count ?? 0}");
                report.AppendLine($"   - عدد المواقع: {locationCount}");
                report.AppendLine($"   - بيانات المواقع: {locationData?.Count ?? 0}");

                if (locationData != null && locationData.Count > 0)
                {
                    report.AppendLine("📍 تفاصيل البيانات المحلية:");
                    foreach (var location in locationData.OrderBy(l => l.Key))
                    {
                        var dataRowsWithValues = location.Value.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();

                        report.AppendLine($"   🏠 الموقع {location.Key}: {location.Value.Count} صف، {dataRowsWithValues} يحتوي على بيانات");
                    }
                }

                System.Diagnostics.Debug.WriteLine(report.ToString());
                MessageBox.Show(report.ToString(), "تقرير فحص البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص البيانات: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء فحص البيانات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// جمع البيانات من جميع الجداول المفتوحة
        /// </summary>
        /// <summary>
        /// جمع البيانات من الجدول الحالي (النظام الجديد)
        /// </summary>
        private void CollectDataFromAllGrids()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 حفظ البيانات من الجدول الحالي");

                // حفظ البيانات الحالية من الجدول المعروض
                SaveCurrentLocationData();

                // عد البيانات المحفوظة
                int totalDataCount = 0;
                foreach (var location in locationData.Values)
                {
                    var dataRowsWithValues = location.Where(r => r.IsDataTypeRow &&
                        r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                    totalDataCount += dataRowsWithValues;
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ البيانات من {locationData.Count} موقع، إجمالي البيانات: {totalDataCount}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جمع البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// إظهار/إخفاء زر حفظ البيانات لجميع المواقع حسب عدد المواقع
        /// </summary>
        private void UpdateSaveAllLocationsButtonVisibility()
        {
            try
            {
                if (SaveAllLocationsButton != null)
                {
                    SaveAllLocationsButton.Visibility = locationCount > 1 ? Visibility.Visible : Visibility.Collapsed;
                    System.Diagnostics.Debug.WriteLine($"🔄 تحديث رؤية زر الحفظ: {(locationCount > 1 ? "مرئي" : "مخفي")} (عدد المواقع: {locationCount})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث رؤية زر الحفظ: {ex.Message}");
            }
        }

        private StackPanel CreateEditableTabHeader(int locationIndex, string locationName)
        {
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var textBlock = new TextBlock
            {
                Text = locationName,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 5, 0)
            };

            var editButton = new Button
            {
                Content = "✏️",
                Width = 20,
                Height = 20,
                FontSize = 10,
                Background = System.Windows.Media.Brushes.Transparent,
                BorderThickness = new Thickness(0),
                Cursor = System.Windows.Input.Cursors.Hand,
                ToolTip = "تحرير اسم الموقع"
            };

            editButton.Click += (s, e) => EditLocationName(locationIndex, textBlock);

            headerPanel.Children.Add(textBlock);
            headerPanel.Children.Add(editButton);

            return headerPanel;
        }

        private void EditLocationName(int locationIndex, TextBlock textBlock)
        {
            var dialog = new LocationNameDialog(textBlock.Text);
            dialog.Owner = this;

            if (dialog.ShowDialog() == true)
            {
                var newName = dialog.LocationName;
                if (!string.IsNullOrWhiteSpace(newName))
                {
                    textBlock.Text = newName;
                    locationNames[locationIndex] = newName;
                }
            }
        }

        // تم إزالة دالة CreateTotalTab لأنها لم تعد مطلوبة مع النظام الجديد

        private void CopyColumnsToDataGrid(DataGrid targetGrid)
        {
            targetGrid.Columns.Clear();

            // نسخ جميع الأعمدة من الجدول الرئيسي
            foreach (var column in IpttDataGrid.Columns)
            {
                if (column is DataGridTextColumn textColumn)
                {
                    // تحديد ما إذا كان العمود قابل للتحرير
                    bool isReadOnly = false;

                    // الأعمدة الثابتة (رقم، مؤشر، نوع البيانات، هدف) للقراءة فقط
                    string headerText = textColumn.Header?.ToString() ?? "";
                    if (headerText == "م" || headerText == "المؤشر" ||
                        headerText == "نوع البيانات" || headerText == "الهدف" ||
                        headerText == "الإنجاز" || headerText == "نسبة الإنجاز")
                    {
                        isReadOnly = true;
                    }

                    // أعمدة الأشهر قابلة للتحرير
                    if (monthColumns.Contains(headerText))
                    {
                        isReadOnly = false; // السماح بالتحرير في أعمدة الأشهر
                    }

                    var newColumn = new DataGridTextColumn
                    {
                        Header = textColumn.Header,
                        Binding = textColumn.Binding,
                        Width = textColumn.Width,
                        IsReadOnly = isReadOnly
                    };

                    // إضافة Style للأعمدة الشهرية لجعل الصفوف الرئيسية مميزة
                    if (textColumn.Header != null && monthColumns.Contains(textColumn.Header.ToString() ?? ""))
                    {
                        var style = new Style(typeof(DataGridCell));
                        var trigger = new DataTrigger();
                        trigger.Binding = new Binding("IsMainIndicator");
                        trigger.Value = true;
                        trigger.Setters.Add(new Setter(DataGridCell.BackgroundProperty, System.Windows.Media.Brushes.LightBlue));
                        trigger.Setters.Add(new Setter(DataGridCell.FontWeightProperty, FontWeights.Bold));
                        style.Triggers.Add(trigger);
                        newColumn.CellStyle = style;
                    }

                    targetGrid.Columns.Add(newColumn);
                }
                else if (column is DataGridTemplateColumn templateColumn)
                {
                    var newTemplateColumn = new DataGridTemplateColumn
                    {
                        Header = templateColumn.Header,
                        Width = templateColumn.Width,
                        CellTemplate = templateColumn.CellTemplate
                    };
                    targetGrid.Columns.Add(newTemplateColumn);
                }
            }
        }

        private ObservableCollection<IpttDisplayRow> CreateLocationDisplayRows()
        {
            var locationRows = new ObservableCollection<IpttDisplayRow>();

            // نسخ البيانات من الجدول الرئيسي
            foreach (var row in displayRows)
            {
                var newRow = new IpttDisplayRow
                {
                    No = row.No,
                    Indicator = row.Indicator,
                    DataType = row.DataType,
                    Target = row.Target, // نسخ الهدف من الصف الأصلي
                    Achievement = "",
                    AchievementPercentage = "",
                    IsMainIndicator = row.IsMainIndicator,
                    IsDataTypeRow = row.IsDataTypeRow,
                    IsTotalRow = row.IsTotalRow,
                    IsCompleted = false,
                    CanDelete = row.CanDelete,
                    IndicatorId = row.IndicatorId,
                    MonthlyData = new Dictionary<string, string>()
                };

                // نسخ البيانات الشهرية (فارغة للمواقع الجديدة)
                foreach (var month in monthColumns)
                {
                    newRow.MonthlyData[month] = "";
                }

                locationRows.Add(newRow);
            }

            return locationRows;
        }

        private ObservableCollection<IpttDisplayRow> CreateTotalDisplayRows()
        {
            var totalRows = new ObservableCollection<IpttDisplayRow>();

            // التأكد من وجود بيانات للاستنساخ
            if (displayRows == null || displayRows.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("لا توجد بيانات لإنشاء تبويب الإجمالي");
                return totalRows;
            }

            // إنشاء صفوف الإجمالي بناءً على البيانات الموجودة
            foreach (var row in displayRows)
            {
                var totalRow = new IpttDisplayRow
                {
                    No = row.No,
                    Indicator = row.Indicator,
                    DataType = row.DataType,
                    Target = row.Target, // نسخ نفس الهدف (وليس الجمع)
                    Achievement = "0", // سيتم حسابه تلقائياً
                    AchievementPercentage = "0%", // سيتم حسابه تلقائياً
                    IsMainIndicator = row.IsMainIndicator,
                    IsDataTypeRow = row.IsDataTypeRow,
                    IsTotalRow = true,
                    IsCompleted = false,
                    CanDelete = false, // لا يمكن حذف صفوف الإجمالي
                    IndicatorId = row.IndicatorId,
                    MonthlyData = new Dictionary<string, string>()
                };

                // تهيئة البيانات الشهرية بالصفر
                foreach (var month in monthColumns)
                {
                    totalRow.MonthlyData[month] = "0";
                }

                totalRows.Add(totalRow);
            }

            System.Diagnostics.Debug.WriteLine($"تم إنشاء {totalRows.Count} صف لتبويب الإجمالي مع حساب الأهداف الإجمالية");
            return totalRows;
        }



        private void CopyDataToAllLocations()
        {
            try
            {
                // التأكد من وجود بيانات للنسخ
                if (displayRows == null || displayRows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات للنسخ إلى المواقع الأخرى");
                    return;
                }

                // حفظ بيانات الموقع 1 (الجدول الرئيسي) أولاً
                locationData[1] = displayRows;
                locationDataGrids[1] = IpttDataGrid;

                // نسخ البيانات من الجدول الرئيسي (موقع 1) إلى جميع المواقع الأخرى
                for (int i = 2; i <= locationCount; i++)
                {
                    if (locationDataGrids.ContainsKey(i))
                    {
                        var locationRows = CreateLocationDisplayRows();
                        locationData[i] = locationRows;

                        // إعداد أعمدة DataGrid للموقع
                        SetupLocationDataGridColumns(locationDataGrids[i]);
                        locationDataGrids[i].ItemsSource = locationRows;

                        // إضافة معالج الأحداث لحساب الإنجاز تلقائياً
                        SetupDataGridEventHandlers(locationDataGrids[i], i);

                        System.Diagnostics.Debug.WriteLine($"تم نسخ {locationRows.Count} صف للموقع {i}");
                    }
                }

                // إضافة معالج الأحداث للجدول الرئيسي
                SetupDataGridEventHandlers(IpttDataGrid, 1);

                // حساب الإنجاز لجميع المواقع
                for (int i = 1; i <= locationCount; i++)
                {
                    CalculateAchievementForLocation(i);
                }

                // تحديث تبويب الإجمالي إذا كان موجوداً
                if (locationCount > 1)
                {
                    UpdateTotalTab();
                }

                // تحديث عرض البيانات لجميع الجداول
                IpttDataGrid.Items.Refresh();
                foreach (var dataGrid in locationDataGrids.Values)
                {
                    dataGrid.Items.Refresh();
                }

                System.Diagnostics.Debug.WriteLine($"تم نسخ البيانات لـ {locationCount} موقع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ البيانات للمواقع: {ex.Message}");
                MessageBox.Show($"خطأ في نسخ البيانات للمواقع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupDataGridEventHandlers(DataGrid dataGrid, int locationIndex)
        {
            // إزالة معالجات الأحداث الموجودة لتجنب التكرار
            dataGrid.CellEditEnding -= DataGrid_CellEditEnding;

            // إضافة معالج الأحداث الجديد
            dataGrid.CellEditEnding += DataGrid_CellEditEnding;

            // حفظ مرجع الموقع للاستخدام في معالج الأحداث
            dataGrid.Tag = locationIndex;
        }

        private void DataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            try
            {
                if (e.EditAction == DataGridEditAction.Commit)
                {
                    var dataGrid = sender as DataGrid;
                    if (dataGrid?.Tag is int locationIndex)
                    {
                        // تأخير الحساب للسماح بحفظ القيمة أولاً
                        Dispatcher.BeginInvoke(new Action(() => {
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"🔄 تحديث البيانات بعد تحرير الخلية - الموقع {locationIndex}");

                                CalculateAchievementForLocation(locationIndex);
                                if (locationCount > 1)
                                {
                                    UpdateTotalTab();
                                }

                                // حفظ فوري للبيانات بعد التحديث
                                System.Diagnostics.Debug.WriteLine("💾 حفظ فوري بعد تحرير الخلية");
                                AutoSaveDataAsync();
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالج الأحداث: {ex.Message}");
                            }
                        }), System.Windows.Threading.DispatcherPriority.Background);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في CellEditEnding: {ex.Message}");
            }
        }

        private void CalculateAchievementForLocation(int locationIndex)
        {
            try
            {
                var rows = locationIndex == 1 ? displayRows :
                          (locationData.ContainsKey(locationIndex) ? locationData[locationIndex] : null);

                if (rows == null) return;

                // حساب الإنجاز لصفوف أنواع البيانات
                foreach (var row in rows)
                {
                    if (!row.IsMainIndicator && !row.IsTotalRow && row.IsDataTypeRow)
                    {
                        // حساب الإنجاز من مجموع الأشهر
                        double totalAchievement = 0;
                        foreach (var month in monthColumns)
                        {
                            if (row.MonthlyData.ContainsKey(month))
                            {
                                var monthValue = row.MonthlyData[month];
                                if (!string.IsNullOrWhiteSpace(monthValue) &&
                                    double.TryParse(monthValue, out double value))
                                {
                                    totalAchievement += value;
                                }
                            }
                        }

                        row.Achievement = totalAchievement.ToString("F0");

                        // حساب النسبة المئوية
                        if (!string.IsNullOrWhiteSpace(row.Target) &&
                            double.TryParse(row.Target, out double target) && target > 0)
                        {
                            double percentage = (totalAchievement / target) * 100;
                            row.AchievementPercentage = $"{percentage:F1}%";
                        }
                        else
                        {
                            row.AchievementPercentage = "0.0%";
                        }
                    }
                }

                // حساب إجمالي المؤشرات الرئيسية
                CalculateMainIndicatorTotals(rows);

                // تحديث عرض البيانات
                var dataGrid = locationIndex == 1 ? IpttDataGrid :
                              (locationDataGrids.ContainsKey(locationIndex) ? locationDataGrids[locationIndex] : null);

                if (dataGrid != null)
                {
                    dataGrid.Items.Refresh();
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ بدلاً من إظهار رسالة للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإنجاز للموقع {locationIndex}: {ex.Message}");
            }
        }

        private void UpdateTotalTab()
        {
            try
            {
                if (!locationData.ContainsKey(0)) return;

                var totalRows = locationData[0];

                // تصحيح: جمع البيانات من جميع المواقع بشكل صحيح
                foreach (var totalRow in totalRows)
                {
                    // معالجة صفوف أنواع البيانات فقط
                    if (totalRow.IsDataTypeRow && !totalRow.IsMainIndicator)
                    {
                        // جمع البيانات الشهرية من جميع المواقع
                        System.Diagnostics.Debug.WriteLine($"🔄 حساب الإجمالي للمؤشر: {totalRow.Indicator} - {totalRow.DataType}");

                        foreach (var month in monthColumns)
                        {
                            double monthTotal = 0;
                            int locationsWithData = 0;

                            // جمع من جميع المواقع (1 إلى locationCount)
                            for (int i = 1; i <= locationCount; i++)
                            {
                                ObservableCollection<IpttDisplayRow>? locationRows = null;

                                if (i == 1)
                                {
                                    locationRows = displayRows;
                                }
                                else if (locationData.ContainsKey(i))
                                {
                                    locationRows = locationData[i];
                                }

                                if (locationRows != null)
                                {
                                    var matchingRow = locationRows.FirstOrDefault(r =>
                                        r.IndicatorId == totalRow.IndicatorId &&
                                        r.DataType == totalRow.DataType &&
                                        r.IsDataTypeRow);

                                    if (matchingRow != null &&
                                        matchingRow.MonthlyData.ContainsKey(month))
                                    {
                                        var monthValue = matchingRow.MonthlyData[month];
                                        if (!string.IsNullOrWhiteSpace(monthValue) &&
                                            double.TryParse(monthValue, out double value) && value > 0)
                                        {
                                            monthTotal += value;
                                            locationsWithData++;
                                            System.Diagnostics.Debug.WriteLine($"   📊 الموقع {i} - {month}: {value}");
                                        }
                                    }
                                }
                            }

                            // تحديث البيانات الشهرية للإجمالي
                            totalRow.MonthlyData[month] = monthTotal > 0 ? monthTotal.ToString("F0") : "0";

                            if (monthTotal > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"   ✅ إجمالي {month}: {monthTotal} (من {locationsWithData} مواقع)");
                            }
                        }

                        // حساب الإنجاز الإجمالي من مجموع الأشهر
                        double totalAchievement = 0;
                        foreach (var month in monthColumns)
                        {
                            if (totalRow.MonthlyData.ContainsKey(month))
                            {
                                var monthValue = totalRow.MonthlyData[month];
                                if (!string.IsNullOrWhiteSpace(monthValue) &&
                                    double.TryParse(monthValue, out double value))
                                {
                                    totalAchievement += value;
                                }
                            }
                        }

                        totalRow.Achievement = totalAchievement.ToString("F0");

                        // نسخ نفس الهدف من الموقع الأول (وليس الجمع)
                        // مثال: إذا كان الهدف في جميع المواقع = 44، فالهدف في الإجمالي = 44 (وليس 44×4=176)
                        string singleTarget = "0";

                        // أخذ الهدف من الموقع الأول فقط
                        var location1Rows = displayRows;
                        if (location1Rows != null)
                        {
                            var matchingRow = location1Rows.FirstOrDefault(r =>
                                r.IndicatorId == totalRow.IndicatorId &&
                                r.DataType == totalRow.DataType &&
                                r.IsDataTypeRow);

                            if (matchingRow != null && !string.IsNullOrWhiteSpace(matchingRow.Target))
                            {
                                singleTarget = matchingRow.Target; // نسخ نفس القيمة بالضبط
                            }
                        }

                        totalRow.Target = singleTarget; // 44 = 44 (وليس 44×4=176)

                        // حساب النسبة المئوية الإجمالية
                        if (!string.IsNullOrWhiteSpace(singleTarget) &&
                            double.TryParse(singleTarget, out double targetValue) &&
                            targetValue > 0)
                        {
                            double percentage = (totalAchievement / targetValue) * 100;
                            totalRow.AchievementPercentage = $"{percentage:F1}%";
                        }
                        else
                        {
                            totalRow.AchievementPercentage = "0.0%";
                        }
                    }
                }

                // حساب إجماليات المؤشرات الرئيسية من جميع المواقع
                CalculateMainIndicatorTotalsFromAllLocations(totalRows);

                // تحديث عرض البيانات
                if (locationDataGrids.ContainsKey(0))
                {
                    locationDataGrids[0].Items.Refresh();
                }

                // إضافة تسجيل للتأكد من التحديث
                System.Diagnostics.Debug.WriteLine($"تم تحديث تبويب الإجمالي بنجاح - عدد الصفوف: {totalRows.Count}");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ مع تفاصيل أكثر
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث تبويب الإجمالي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // إظهار رسالة للمستخدم في حالة الخطأ الحرج
                MessageBox.Show($"حدث خطأ في تحديث تبويب الإجمالي:\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ExportToExcel(string filePath)
        {
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                if (locationCount == 1)
                {
                    // تصدير عادي لموقع واحد
                    var worksheet = package.Workbook.Worksheets.Add("IPTT");
                    worksheet.View.RightToLeft = true;
                    CreateProjectInfoSection(worksheet);
                    CreateIpttTableForLocation(worksheet, displayRows, "الجدول الرئيسي");
                    worksheet.Cells.AutoFitColumns();
                }
                else
                {
                    // تصدير متعدد الأوراق للمواقع المتعددة

                    // إنشاء ورقة للجدول الرئيسي (موقع 1)
                    var mainLocationName = locationNames.ContainsKey(1) ? locationNames[1] : "موقع 1";
                    var mainWorksheet = package.Workbook.Worksheets.Add(SanitizeSheetName(mainLocationName));
                    mainWorksheet.View.RightToLeft = true;
                    CreateProjectInfoSection(mainWorksheet);
                    CreateIpttTableForLocation(mainWorksheet, displayRows, mainLocationName);
                    mainWorksheet.Cells.AutoFitColumns();

                    // إنشاء ورقة لكل موقع إضافي
                    for (int i = 2; i <= locationCount; i++)
                    {
                        if (locationData.ContainsKey(i) && locationNames.ContainsKey(i))
                        {
                            var locationName = SanitizeSheetName(locationNames[i]);
                            var worksheet = package.Workbook.Worksheets.Add(locationName);
                            worksheet.View.RightToLeft = true;
                            CreateProjectInfoSection(worksheet);
                            CreateIpttTableForLocation(worksheet, locationData[i], locationNames[i]);
                            worksheet.Cells.AutoFitColumns();
                        }
                    }

                    // إنشاء ورقة الإجمالي (عرض البيانات كما هي في التبويب)
                    if (locationData.ContainsKey(0) && locationNames.ContainsKey(0))
                    {
                        var totalName = SanitizeSheetName(locationNames[0]);
                        var totalWorksheet = package.Workbook.Worksheets.Add(totalName);
                        totalWorksheet.View.RightToLeft = true;
                        CreateProjectInfoSection(totalWorksheet);
                        CreateIpttTableForLocation(totalWorksheet, locationData[0], locationNames[0]);
                        totalWorksheet.Cells.AutoFitColumns();
                    }
                }

                // Save the file
                var fileInfo = new FileInfo(filePath);
                package.SaveAs(fileInfo);
            }
        }

        private string SanitizeSheetName(string name)
        {
            // إزالة الأحرف غير المسموحة في أسماء أوراق Excel
            var invalidChars = new char[] { '\\', '/', '*', '?', ':', '[', ']' };
            var sanitized = name;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // تحديد الطول الأقصى (31 حرف)
            if (sanitized.Length > 31)
            {
                sanitized = sanitized.Substring(0, 31);
            }

            return sanitized;
        }

        private void CreateProjectInfoSection(OfficeOpenXml.ExcelWorksheet worksheet)
        {
            // Title
            worksheet.Cells["A1"].Value = "جدول المؤشرات والأهداف (IPTT)";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells["A1:M1"].Merge = true;

            // Project information
            worksheet.Cells["A3"].Value = "اسم المشروع:";
            worksheet.Cells["B3"].Value = currentProject.Name;
            worksheet.Cells["A3"].Style.Font.Bold = true;

            worksheet.Cells["A4"].Value = "مدير المشروع:";
            worksheet.Cells["B4"].Value = currentProject.Manager;
            worksheet.Cells["A4"].Style.Font.Bold = true;

            var duration = (currentProject.EndDate - currentProject.StartDate).Days;
            var months = Math.Ceiling(duration / 30.0);
            worksheet.Cells["A5"].Value = "مدة المشروع:";
            worksheet.Cells["B5"].Value = $"{months:F0} شهر من {currentProject.StartDate:yyyy/MM/dd} إلى {currentProject.EndDate:yyyy/MM/dd}";
            worksheet.Cells["A5"].Style.Font.Bold = true;

            worksheet.Cells["A6"].Value = "مواقع المشروع:";
            worksheet.Cells["B6"].Value = string.IsNullOrWhiteSpace(currentProject.Region) ? "غير محدد" : currentProject.Region;
            worksheet.Cells["A6"].Style.Font.Bold = true;
        }

        private void CreateIpttTableForLocation(OfficeOpenXml.ExcelWorksheet worksheet, ObservableCollection<IpttDisplayRow> rows, string locationName)
        {
            int startRow = 9;
            int currentRow = startRow;

            // إضافة عنوان الموقع
            worksheet.Cells[7, 1].Value = $"بيانات: {locationName}";
            worksheet.Cells[7, 1].Style.Font.Bold = true;
            worksheet.Cells[7, 1].Style.Font.Size = 14;
            worksheet.Cells[7, 1].Style.Font.Color.SetColor(System.Drawing.Color.FromArgb(74, 144, 226));

            // Create headers - updated to match the display
            var headers = new List<string> { "رقم", "المؤشر", "نوع البيانات", "الهدف", "الإنجاز", "الإنجاز %" };
            headers.AddRange(monthColumns);

            for (int col = 0; col < headers.Count; col++)
            {
                var cell = worksheet.Cells[currentRow, col + 1];
                cell.Value = headers[col];
                cell.Style.Font.Bold = true;
                // Set background color for header
                cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(74, 144, 226)); // Blue
                cell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                cell.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                cell.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
                cell.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            currentRow++;

            // Export all display rows exactly as they appear in the grid
            foreach (var displayRow in rows)
            {
                AddDisplayRowToExcel(worksheet, currentRow, displayRow, headers.Count);
                currentRow++;
            }

            // Apply borders to the entire table
            var tableRange = worksheet.Cells[startRow, 1, currentRow - 1, headers.Count];
            tableRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            tableRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            tableRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            tableRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        private void AddDisplayRowToExcel(OfficeOpenXml.ExcelWorksheet worksheet, int row, IpttDisplayRow displayRow, int totalColumns)
        {
            // Add basic data
            worksheet.Cells[row, 1].Value = displayRow.No;
            worksheet.Cells[row, 2].Value = displayRow.Indicator;
            worksheet.Cells[row, 3].Value = displayRow.DataType;
            worksheet.Cells[row, 4].Value = displayRow.Target;
            worksheet.Cells[row, 5].Value = displayRow.Achievement;
            worksheet.Cells[row, 6].Value = displayRow.AchievementPercentage;

            // Add monthly data
            for (int i = 0; i < monthColumns.Count; i++)
            {
                var month = monthColumns[i];
                var value = displayRow.MonthlyData.ContainsKey(month) ? displayRow.MonthlyData[month] : "0";
                worksheet.Cells[row, 7 + i].Value = value;
            }

            // Apply styling based on row type
            for (int col = 1; col <= totalColumns; col++)
            {
                var cell = worksheet.Cells[row, col];
                cell.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                cell.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
                cell.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);

                // Apply background colors based on row type
                if (displayRow.IsMainIndicator)
                {
                    cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(230, 245, 230)); // Light green
                    cell.Style.Font.Bold = true;
                }
                else if (displayRow.IsTotalRow)
                {
                    cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(243, 229, 245)); // Light purple
                    cell.Style.Font.Bold = true;
                }
                else if (displayRow.IsDataTypeRow)
                {
                    cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(248, 249, 250)); // Light gray
                }
            }
        }

        private void SetupLocationDataGridColumns(DataGrid dataGrid)
        {
            dataGrid.Columns.Clear();

            // Fixed columns
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "رقم",
                Binding = new Binding("No"),
                Width = new DataGridLength(60),
                IsReadOnly = true
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "المؤشر",
                Binding = new Binding("Indicator"),
                Width = new DataGridLength(200),
                IsReadOnly = true
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "نوع البيانات",
                Binding = new Binding("DataType"),
                Width = new DataGridLength(120),
                IsReadOnly = true
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الهدف",
                Binding = new Binding("Target"),
                Width = new DataGridLength(80),
                IsReadOnly = true // الهدف للقراءة فقط (يُنسخ تلقائياً)
            });

            // Monthly columns - قابلة للتحرير
            foreach (var month in monthColumns)
            {
                var monthColumn = new DataGridTextColumn
                {
                    Header = month,
                    Binding = new Binding($"MonthlyData[{month}]"),
                    Width = new DataGridLength(80),
                    IsReadOnly = false // السماح بالتحرير في أعمدة الأشهر
                };

                // إضافة Style لتمييز الصفوف الرئيسية بصرياً فقط (بدون تعطيل)
                var style = new Style(typeof(DataGridCell));
                var trigger = new DataTrigger();
                trigger.Binding = new Binding("IsMainIndicator");
                trigger.Value = true;
                trigger.Setters.Add(new Setter(DataGridCell.BackgroundProperty, System.Windows.Media.Brushes.LightBlue));
                trigger.Setters.Add(new Setter(DataGridCell.FontWeightProperty, FontWeights.Bold));
                style.Triggers.Add(trigger);
                monthColumn.CellStyle = style;

                dataGrid.Columns.Add(monthColumn);
            }

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الإنجاز",
                Binding = new Binding("Achievement"),
                Width = new DataGridLength(80),
                IsReadOnly = true
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الإنجاز %",
                Binding = new Binding("AchievementPercentage"),
                Width = new DataGridLength(80),
                IsReadOnly = true
            });

            // لا نحتاج زر الحذف في تبويبات المواقع الأخرى
        }

        /// <summary>
        /// تحميل بيانات IPTT من قاعدة البيانات
        /// </summary>
        private async void LoadIpttFromDatabaseAsync()
        {
            try
            {
                // التحقق من صحة البيانات قبل التحميل
                if (currentProject == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ خطأ: currentProject is null");
                    InitializeEmptyData();
                    return;
                }

                if (databaseService == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ خطأ: databaseService is null");
                    databaseService = new IpttDatabaseService();
                }

                System.Diagnostics.Debug.WriteLine($"🔄 بدء تحميل بيانات IPTT للمشروع: {currentProject.Id}");
                var loadResult = await databaseService.LoadIpttDataAsync(currentProject.Id.ToString());

                if (loadResult != null && loadResult.Indicators != null && loadResult.Indicators.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على بيانات IPTT: {loadResult.Indicators.Count} مؤشر، {loadResult.LocationData.Count} موقع");

                    // تحديث البيانات المحملة
                    ipttData = loadResult.Indicators;
                    monthColumns = loadResult.MonthColumns ?? new List<string>();
                    locationData = loadResult.LocationData ?? new Dictionary<int, ObservableCollection<IpttDisplayRow>>();

                    // طباعة تفاصيل البيانات المحملة
                    foreach (var location in locationData)
                    {
                        System.Diagnostics.Debug.WriteLine($"📍 الموقع {location.Key}: {location.Value.Count} صف");
                        var dataRows = location.Value.Where(r => r.IsDataTypeRow && r.MonthlyData.Any(m => !string.IsNullOrEmpty(m.Value)));
                        System.Diagnostics.Debug.WriteLine($"   - صفوف تحتوي على بيانات: {dataRows.Count()}");
                    }

                    // تحديث عدد المواقع بناءً على البيانات المحملة
                    locationCount = loadResult.LocationCount;
                    System.Diagnostics.Debug.WriteLine($"📊 عدد المواقع المحمل: {locationCount}");

                    // التأكد من أن عدد المواقع صحيح
                    if (locationCount <= 0)
                    {
                        locationCount = locationData.Keys.Count > 0 ? locationData.Keys.Where(k => k > 0).Max() : 1;
                        System.Diagnostics.Debug.WriteLine($"🔧 تم تصحيح عدد المواقع إلى: {locationCount}");
                    }

                    // تحديث LocationCountTextBox
                    LocationCountTextBox.Text = locationCount.ToString();

                    // إضافة الموقع الرئيسي إذا لم يكن موجوداً
                    if (!locationData.ContainsKey(1))
                    {
                        // إنشاء بيانات الموقع الرئيسي من المؤشرات المحملة
                        locationData[1] = CreateDisplayRowsFromIndicators();
                    }

                    // تحديث displayRows للموقع الرئيسي
                    displayRows.Clear();
                    foreach (var row in locationData[1])
                    {
                        displayRows.Add(row);
                    }

                    // إعداد الأعمدة الشهرية
                    SetupDataGridColumns();

                    // ربط البيانات بالجدول الرئيسي أولاً
                    if (locationData.ContainsKey(1))
                    {
                        displayRows.Clear();
                        foreach (var row in locationData[1])
                        {
                            displayRows.Add(row);
                        }
                        IpttDataGrid.ItemsSource = displayRows;
                        System.Diagnostics.Debug.WriteLine($"✅ تم ربط {displayRows.Count} صف بالجدول الرئيسي");
                    }

                    // تهيئة أسماء المواقع
                    for (int i = 1; i <= locationCount; i++)
                    {
                        if (!locationNames.ContainsKey(i))
                        {
                            locationNames[i] = $"الموقع {i}";
                        }
                    }

                    if (locationCount > 1 && !locationNames.ContainsKey(0))
                    {
                        locationNames[0] = "الإجمالي";
                    }

                    // تهيئة القائمة المنسدلة
                    InitializeLocationComboBox();

                    // تحديث الحسابات لجميع المواقع
                    for (int i = 1; i <= locationCount; i++)
                    {
                        if (locationData.ContainsKey(i))
                        {
                            CalculateAchievementForLocationData(locationData[i]);
                        }
                    }

                    // تحديث الإجمالي إذا كان موجوداً
                    if (locationCount > 1 && locationData.ContainsKey(0))
                    {
                        UpdateTotalFromAllLocations();
                    }

                        // طباعة حالة الربط النهائية
                        System.Diagnostics.Debug.WriteLine($"📋 حالة الربط النهائية:");
                        foreach (var kvp in locationDataGrids)
                        {
                            var items = kvp.Value.ItemsSource as ObservableCollection<IpttDisplayRow>;
                            var itemsCount = items?.Count ?? 0;
                            var dataRowsWithValues = items?.Where(r => r.IsDataTypeRow &&
                                r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count() ?? 0;
                            System.Diagnostics.Debug.WriteLine($"   - الموقع {kvp.Key}: {itemsCount} صف مربوط، {dataRowsWithValues} صف يحتوي على بيانات");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("📝 موقع واحد - ربط البيانات مباشرة");
                        // للموقع الواحد، ربط البيانات مباشرة
                        if (locationData.ContainsKey(1))
                        {
                            displayRows.Clear();
                            foreach (var row in locationData[1])
                            {
                                displayRows.Add(row);
                            }
                            SetupDataGridColumns();
                            IpttDataGrid.ItemsSource = displayRows;
                            System.Diagnostics.Debug.WriteLine($"✅ تم ربط {displayRows.Count} صف للموقع الواحد");
                        }
                    }

                    // تحديث الحسابات بعد التحميل
                    RefreshAllCalculations();

                    // رسالة تأكيد التحميل مع تفاصيل أكثر
                    var indicatorCount = ipttData?.Count ?? 0;
                    var locationCountMsg = locationCount > 1 ? $" و {locationCount} مواقع" : "";

                    // حساب عدد البيانات الشهرية المحملة
                    int totalDataEntries = 0;
                    foreach (var location in locationData)
                    {
                        foreach (var row in location.Value.Where(r => r.IsDataTypeRow))
                        {
                            totalDataEntries += row.MonthlyData.Count(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0");
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"🎉 تم تحميل البيانات بنجاح: {indicatorCount} مؤشر، {locationCount} موقع، {totalDataEntries} قيمة شهرية");

                    // فحص البيانات المحملة
                    ValidateLoadedData();

                    // فحص قاعدة البيانات مباشرة للمقارنة
                    InspectDatabaseForProject(currentProject.Name);

                    // إعادة تحميل بالقوة للتأكد من عرض البيانات
                    await Task.Delay(500);
                    ForceReloadDataFromDatabase();

                    MessageBox.Show($"تم تحميل بيانات IPTT بنجاح!\nعدد المؤشرات: {indicatorCount}{locationCountMsg}\nعدد القيم الشهرية: {totalDataEntries}",
                                  "تحميل البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // لا توجد بيانات محفوظة، إنشاء بيانات فارغة
                    InitializeEmptyData();
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات محفوظة، تم إنشاء بيانات فارغة");
                }

                // تحديث رؤية زر الحفظ الشامل بعد التحميل
                UpdateSaveAllLocationsButtonVisibility();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                // في حالة فشل التحميل، إنشاء بيانات فارغة
                try
                {
                    InitializeEmptyData();
                }
                catch (Exception initEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة البيانات الفارغة: {initEx.Message}");
                    // تهيئة أساسية جداً
                    ipttData = new ObservableCollection<IpttIndicator>();
                    displayRows = new ObservableCollection<IpttDisplayRow>();
                    locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();
                    locationCount = 1;
                    LocationCountTextBox.Text = "1";
                }

                MessageBox.Show($"تعذر تحميل البيانات من قاعدة البيانات: {ex.Message}\nسيتم البدء ببيانات فارغة.",
                              "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// إنشاء صفوف العرض من المؤشرات المحملة
        /// </summary>
        private ObservableCollection<IpttDisplayRow> CreateDisplayRowsFromIndicators()
        {
            var rows = new ObservableCollection<IpttDisplayRow>();

            foreach (var indicator in ipttData)
            {
                // إضافة صف المؤشر الرئيسي
                var mainRow = new IpttDisplayRow
                {
                    No = indicator.No,
                    Indicator = indicator.Indicator,
                    DataType = "",
                    Target = "",
                    Achievement = "",
                    AchievementPercentage = "",
                    IsMainIndicator = true,
                    IsDataTypeRow = false,
                    IsTotalRow = false,
                    IsCompleted = false,
                    CanDelete = true,
                    IndicatorId = indicator.No,
                    MonthlyData = new Dictionary<string, string>()
                };

                // تهيئة البيانات الشهرية للمؤشر الرئيسي
                foreach (var month in monthColumns)
                {
                    mainRow.MonthlyData[month] = "";
                }

                rows.Add(mainRow);

                // إضافة صفوف أنواع البيانات
                foreach (var dataType in indicator.DataTypes)
                {
                    var dataTypeRow = new IpttDisplayRow
                    {
                        No = "",
                        Indicator = "",
                        DataType = dataType.Name,
                        Target = dataType.Target,
                        Achievement = "",
                        AchievementPercentage = "",
                        IsMainIndicator = false,
                        IsDataTypeRow = true,
                        IsTotalRow = false,
                        IsCompleted = false,
                        CanDelete = false,
                        IndicatorId = indicator.No,
                        MonthlyData = new Dictionary<string, string>()
                    };

                    // تهيئة البيانات الشهرية لنوع البيانات
                    foreach (var month in monthColumns)
                    {
                        // استخدام القيمة الموجودة إذا كانت متوفرة
                        if (dataType.MonthlyValues != null && dataType.MonthlyValues.ContainsKey(month))
                        {
                            dataTypeRow.MonthlyData[month] = dataType.MonthlyValues[month];
                        }
                        else
                        {
                            dataTypeRow.MonthlyData[month] = "";
                        }
                    }

                    rows.Add(dataTypeRow);
                }
            }

            return rows;
        }

        /// <summary>
        /// تهيئة بيانات فارغة
        /// </summary>
        private void InitializeEmptyData()
        {
            ipttData = new ObservableCollection<IpttIndicator>();
            displayRows = new ObservableCollection<IpttDisplayRow>();
            GenerateMonthColumns();

            // التأكد من أن locationCount له قيمة صحيحة
            if (locationCount <= 0)
            {
                locationCount = 1; // قيمة افتراضية
                LocationCountTextBox.Text = "1";
            }

            // تهيئة بيانات المواقع
            locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();
            for (int i = 1; i <= locationCount; i++)
            {
                locationData[i] = new ObservableCollection<IpttDisplayRow>();
            }

            // إنشاء الجدول بعد تهيئة البيانات الفارغة
            GenerateIpttTable();
            System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء بيانات فارغة وجدول IPTT مع {locationCount} موقع");
        }

        /// <summary>
        /// حفظ بيانات IPTT في قاعدة البيانات
        /// </summary>
        private async Task SaveIpttToDatabaseAsync()
        {
            try
            {
                // تحديث البيانات الأساسية قبل الحفظ
                UpdateUnderlyingDataFromDisplayRows();

                // حفظ البيانات في قاعدة البيانات
                bool success = await databaseService.SaveIpttDataAsync(
                    currentProject,
                    ipttData,
                    monthColumns,
                    locationData
                );

                if (!success)
                {
                    throw new Exception("فشل في حفظ البيانات في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ البيانات في قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ نسخة احتياطية في ملف JSON
        /// </summary>
        private void SaveIpttToJsonFile()
        {
            try
            {
                // إنشاء بيانات الحفظ
                var saveData = new
                {
                    ProjectId = currentProject.Id.ToString(),
                    ProjectName = currentProject.Name,
                    ProjectManager = currentProject.Manager,
                    Status = currentProject.Status,
                    StartDate = currentProject.StartDate,
                    EndDate = currentProject.EndDate,
                    Budget = currentProject.Budget,
                    Description = currentProject.Description,
                    Indicators = ipttData.Select(indicator => new
                    {
                        No = indicator.No,
                        Indicator = indicator.Indicator,
                        DataTypes = indicator.DataTypes.Select(dt => new
                        {
                            Name = dt.Name,
                            Target = dt.Target,
                            MonthlyValues = dt.MonthlyValues
                        }).ToList()
                    }).ToList(),
                    MonthColumns = monthColumns,
                    LocationData = locationData.ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value.Where(r => r.IsDataTypeRow).Select(r => new
                        {
                            IndicatorId = r.IndicatorId,
                            DataType = r.DataType,
                            Target = r.Target,
                            MonthlyData = r.MonthlyData
                        }).ToList()
                    ),
                    SavedDate = DateTime.Now
                };

                var json = JsonSerializer.Serialize(saveData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                // حفظ النسخة الاحتياطية
                var fileName = $"IPTT_Backup_{SanitizeFileName(currentProject.Name)}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "TYF_IPTT_Backups", fileName);

                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                File.WriteAllText(filePath, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // فشل الحفظ الاحتياطي لا يجب أن يوقف العملية الرئيسية
                System.Diagnostics.Debug.WriteLine($"فشل في حفظ النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف اسم الملف من الأحرف غير المسموحة
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        /// <summary>
        /// تحديث البيانات الأساسية من صفوف العرض
        /// </summary>
        private void UpdateUnderlyingDataFromDisplayRows()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحديث البيانات الأساسية من صفوف العرض");

                // تحديث البيانات من الموقع الرئيسي (الموقع 1)
                UpdateDataFromDisplayRows(displayRows, 1);

                // التأكد من أن بيانات الموقع 1 محفوظة في locationData
                if (!locationData.ContainsKey(1))
                {
                    locationData[1] = new ObservableCollection<IpttDisplayRow>();
                }

                // نسخ البيانات المحدثة إلى locationData[1]
                locationData[1].Clear();
                foreach (var row in displayRows)
                {
                    locationData[1].Add(row);
                }

                // تحديث البيانات من المواقع الأخرى
                System.Diagnostics.Debug.WriteLine($"🔄 تحديث البيانات من {locationData.Count - 1} موقع إضافي");

                foreach (var locationKvp in locationData.ToList()) // استخدام ToList لتجنب تعديل المجموعة أثناء التكرار
                {
                    if (locationKvp.Key > 1) // تجاهل الموقع 1 لأنه تم تحديثه بالفعل
                    {
                        System.Diagnostics.Debug.WriteLine($"🔄 معالجة الموقع {locationKvp.Key}");

                        // التحقق من وجود DataGrid للموقع
                        if (locationDataGrids.ContainsKey(locationKvp.Key))
                        {
                            var dataGrid = locationDataGrids[locationKvp.Key];

                            // التأكد من أن البيانات محدثة من DataGrid
                            if (dataGrid.ItemsSource is ObservableCollection<IpttDisplayRow> currentRows)
                            {
                                // تحديث locationData بالبيانات الحالية من DataGrid
                                locationData[locationKvp.Key] = currentRows;

                                // تحديث البيانات الأساسية
                                UpdateDataFromDisplayRows(currentRows, locationKvp.Key);

                                var dataRowsWithValues = currentRows.Where(r => r.IsDataTypeRow &&
                                    r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث بيانات الموقع {locationKvp.Key}: {currentRows.Count} صف، {dataRowsWithValues} صف يحتوي على بيانات");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"⚠️ الموقع {locationKvp.Key}: DataGrid غير مربوط بالبيانات");
                            }
                        }
                        else
                        {
                            // إذا لم يكن هناك DataGrid، استخدم البيانات الموجودة
                            UpdateDataFromDisplayRows(locationKvp.Value, locationKvp.Key);

                            var dataRowsWithValues = locationKvp.Value.Where(r => r.IsDataTypeRow &&
                                r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                            System.Diagnostics.Debug.WriteLine($"✅ تم تحديث بيانات الموقع {locationKvp.Key} من البيانات المحفوظة: {locationKvp.Value.Count} صف، {dataRowsWithValues} صف يحتوي على بيانات");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحديث البيانات لـ {locationData.Count} موقع");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث البيانات الأساسية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث البيانات من صفوف عرض موقع محدد
        /// </summary>
        private void UpdateDataFromDisplayRows(ObservableCollection<IpttDisplayRow> rows, int locationNumber)
        {
            System.Diagnostics.Debug.WriteLine($"🔄 تحديث البيانات الأساسية للموقع {locationNumber}");

            foreach (var row in rows.Where(r => r.IsDataTypeRow))
            {
                // العثور على المؤشر المقابل
                var indicator = ipttData.FirstOrDefault(i => i.No == row.IndicatorId);
                if (indicator != null)
                {
                    // العثور على نوع البيانات المقابل
                    var dataType = indicator.DataTypes.FirstOrDefault(dt => dt.Name == row.DataType);
                    if (dataType != null)
                    {
                        // إنشاء مفتاح فريد للموقع إذا لم يكن موجوداً
                        var locationKey = $"Location_{locationNumber}";

                        // تحديث البيانات الشهرية للموقع المحدد
                        foreach (var monthData in row.MonthlyData)
                        {
                            if (locationNumber == 1)
                            {
                                // الموقع الرئيسي - تحديث البيانات الأساسية
                                dataType.MonthlyValues[monthData.Key] = monthData.Value;
                            }
                            else
                            {
                                // المواقع الأخرى - حفظ البيانات بمفتاح الموقع
                                var locationMonthKey = $"{locationKey}_{monthData.Key}";
                                dataType.MonthlyValues[locationMonthKey] = monthData.Value;
                            }
                        }

                        System.Diagnostics.Debug.WriteLine($"   ✅ تم تحديث {row.IndicatorId}-{row.DataType} للموقع {locationNumber}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"   ⚠️ لم يتم العثور على نوع البيانات: {row.DataType} للمؤشر {row.IndicatorId}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"   ⚠️ لم يتم العثور على المؤشر: {row.IndicatorId}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ تم تحديث البيانات الأساسية للموقع {locationNumber}");
        }

        private void CalculateMainIndicatorTotalsFromAllLocations(ObservableCollection<IpttDisplayRow> totalRows)
        {
            try
            {
                // تجميع الصفوف حسب المؤشر
                var indicatorGroups = totalRows.GroupBy(r => r.IndicatorId);

                foreach (var group in indicatorGroups)
                {
                    var mainIndicatorRow = group.FirstOrDefault(r => r.IsMainIndicator);
                    var dataTypeRows = group.Where(r => r.IsDataTypeRow).ToList();

                    if (mainIndicatorRow != null && dataTypeRows.Any())
                    {
                        // حساب إجمالي كل شهر من جميع أنواع البيانات
                        foreach (var month in monthColumns)
                        {
                            double monthTotal = 0;
                            foreach (var dataRow in dataTypeRows)
                            {
                                if (dataRow.MonthlyData.ContainsKey(month))
                                {
                                    var monthValue = dataRow.MonthlyData[month];
                                    if (!string.IsNullOrWhiteSpace(monthValue) &&
                                        double.TryParse(monthValue, out double value))
                                    {
                                        monthTotal += value;
                                    }
                                }
                            }
                            mainIndicatorRow.MonthlyData[month] = monthTotal.ToString("F0");
                        }

                        // حساب الإنجاز الإجمالي
                        double totalAchievement = 0;
                        foreach (var month in monthColumns)
                        {
                            if (mainIndicatorRow.MonthlyData.ContainsKey(month))
                            {
                                var monthValue = mainIndicatorRow.MonthlyData[month];
                                if (!string.IsNullOrWhiteSpace(monthValue) &&
                                    double.TryParse(monthValue, out double value))
                                {
                                    totalAchievement += value;
                                }
                            }
                        }
                        mainIndicatorRow.Achievement = totalAchievement.ToString("F0");

                        // حساب الهدف الإجمالي من جميع أنواع البيانات
                        double totalTarget = 0;
                        foreach (var dataRow in dataTypeRows)
                        {
                            if (!string.IsNullOrWhiteSpace(dataRow.Target) &&
                                double.TryParse(dataRow.Target, out double target))
                            {
                                totalTarget += target;
                            }
                        }
                        mainIndicatorRow.Target = totalTarget.ToString("F0");

                        // حساب النسبة المئوية
                        if (totalTarget > 0)
                        {
                            double percentage = (totalAchievement / totalTarget) * 100;
                            mainIndicatorRow.AchievementPercentage = $"{percentage:F1}%";
                        }
                        else
                        {
                            mainIndicatorRow.AchievementPercentage = "0.0%";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجماليات المؤشرات الرئيسية: {ex.Message}");
            }
        }

        private void CalculateMainIndicatorTotals(ObservableCollection<IpttDisplayRow> rows)
        {
            try
            {
                // تجميع الصفوف حسب المؤشر
                var indicatorGroups = rows.GroupBy(r => r.IndicatorId);

                foreach (var group in indicatorGroups)
                {
                    var mainIndicatorRow = group.FirstOrDefault(r => r.IsMainIndicator);
                    var dataTypeRows = group.Where(r => r.IsDataTypeRow).ToList();

                    if (mainIndicatorRow != null && dataTypeRows.Any())
                    {
                        // حساب إجمالي كل شهر
                        foreach (var month in monthColumns)
                        {
                            double monthTotal = 0;
                            foreach (var dataRow in dataTypeRows)
                            {
                                if (dataRow.MonthlyData.ContainsKey(month))
                                {
                                    var monthValue = dataRow.MonthlyData[month];
                                    if (!string.IsNullOrWhiteSpace(monthValue) &&
                                        double.TryParse(monthValue, out double value))
                                    {
                                        monthTotal += value;
                                    }
                                }
                            }
                            mainIndicatorRow.MonthlyData[month] = monthTotal.ToString("F0");
                        }

                        // حساب الإنجاز الإجمالي للمؤشر
                        double totalAchievement = 0;
                        foreach (var month in monthColumns)
                        {
                            if (mainIndicatorRow.MonthlyData.ContainsKey(month))
                            {
                                var monthValue = mainIndicatorRow.MonthlyData[month];
                                if (!string.IsNullOrWhiteSpace(monthValue) &&
                                    double.TryParse(monthValue, out double value))
                                {
                                    totalAchievement += value;
                                }
                            }
                        }

                        mainIndicatorRow.Achievement = totalAchievement.ToString("F0");

                        // حساب الهدف الإجمالي للمؤشر
                        double totalTarget = 0;
                        foreach (var dataRow in dataTypeRows)
                        {
                            if (!string.IsNullOrWhiteSpace(dataRow.Target) &&
                                double.TryParse(dataRow.Target, out double target))
                            {
                                totalTarget += target;
                            }
                        }

                        mainIndicatorRow.Target = totalTarget.ToString("F0");

                        // حساب النسبة المئوية الإجمالية
                        if (totalTarget > 0)
                        {
                            double percentage = (totalAchievement / totalTarget) * 100;
                            mainIndicatorRow.AchievementPercentage = $"{percentage:F1}%";
                        }
                        else
                        {
                            mainIndicatorRow.AchievementPercentage = "0.0%";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجماليات المؤشرات الرئيسية: {ex.Message}");
            }
        }

        private void ShowCalculationSummary()
        {
            try
            {
                var summary = "ملخص الحسابات:\n\n";

                // عرض ملخص لكل موقع
                for (int i = 1; i <= locationCount; i++)
                {
                    var locationName = locationNames.ContainsKey(i) ? locationNames[i] : $"موقع {i}";
                    summary += $"📍 {locationName}:\n";

                    var rows = i == 1 ? displayRows :
                              (locationData.ContainsKey(i) ? locationData[i] : null);

                    if (rows != null)
                    {
                        foreach (var row in rows.Where(r => r.IsDataTypeRow))
                        {
                            summary += $"   • {row.DataType}: الإنجاز = {row.Achievement}, النسبة = {row.AchievementPercentage}\n";
                        }
                    }
                    summary += "\n";
                }

                // عرض الإجمالي إذا كان موجوداً
                if (locationCount > 1 && locationData.ContainsKey(0))
                {
                    summary += "📊 الإجمالي:\n";
                    var totalRows = locationData[0];
                    foreach (var row in totalRows.Where(r => r.IsDataTypeRow))
                    {
                        summary += $"   • {row.DataType}: الإنجاز = {row.Achievement}, النسبة = {row.AchievementPercentage}\n";
                    }
                }

                System.Diagnostics.Debug.WriteLine(summary);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض ملخص الحسابات: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                databaseService?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }

    // نافذة حوار لتحرير اسم الموقع
    public class LocationNameDialog : Window
    {
        private TextBox nameTextBox;
        public string LocationName { get; private set; }

        public LocationNameDialog(string currentName)
        {
            Title = "تحرير اسم الموقع";
            Width = 300;
            Height = 150;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;
            ResizeMode = ResizeMode.NoResize;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var label = new TextBlock
            {
                Text = "اسم الموقع:",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10)
            };
            Grid.SetRow(label, 0);

            nameTextBox = new TextBox
            {
                Text = currentName,
                FontSize = 12,
                Padding = new Thickness(5),
                Margin = new Thickness(10),
                Height = 30
            };
            Grid.SetRow(nameTextBox, 1);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };

            var okButton = new Button
            {
                Content = "موافق",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)),
                Foreground = System.Windows.Media.Brushes.White,
                BorderThickness = new Thickness(0)
            };
            okButton.Click += (s, e) => { LocationName = nameTextBox.Text; DialogResult = true; Close(); };

            var cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(117, 117, 117)),
                Foreground = System.Windows.Media.Brushes.White,
                BorderThickness = new Thickness(0)
            };
            cancelButton.Click += (s, e) => { DialogResult = false; Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            Grid.SetRow(buttonPanel, 2);

            grid.Children.Add(label);
            grid.Children.Add(nameTextBox);
            grid.Children.Add(buttonPanel);

            Content = grid;
            nameTextBox.Focus();
            nameTextBox.SelectAll();
        }
    }

    // إضافة دالة الحفظ التلقائي في نهاية الكلاس الرئيسي
    public partial class IPTTDesignWindow
    {
        /// <summary>
        /// تحديث البيانات من جميع الجداول المفتوحة
        /// </summary>
        private void UpdateDataFromAllDataGrids()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 تحديث البيانات من جميع الجداول المفتوحة");

                // تحديث البيانات من الجدول الرئيسي (الموقع 1)
                if (IpttDataGrid.ItemsSource is ObservableCollection<IpttDisplayRow> mainRows)
                {
                    displayRows = mainRows;
                    locationData[1] = new ObservableCollection<IpttDisplayRow>(mainRows);
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث بيانات الموقع الرئيسي: {mainRows.Count} صف");
                }

                // تحديث البيانات من جداول المواقع الأخرى
                foreach (var kvp in locationDataGrids)
                {
                    if (kvp.Key > 1 && kvp.Value.ItemsSource is ObservableCollection<IpttDisplayRow> locationRows)
                    {
                        locationData[kvp.Key] = new ObservableCollection<IpttDisplayRow>(locationRows);

                        var dataRowsWithValues = locationRows.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحديث بيانات الموقع {kvp.Key}: {locationRows.Count} صف، {dataRowsWithValues} صف يحتوي على بيانات");
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث البيانات من جميع الجداول");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث البيانات من الجداول: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ تلقائي للبيانات
        /// </summary>
        private async void AutoSaveDataAsync()
        {
            try
            {
                if (currentProject == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد مشروع محدد للحفظ");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"💾 بدء الحفظ التلقائي للمشروع: {currentProject.Name} (ID: {currentProject.Id})");

                // جمع البيانات من جميع الجداول المفتوحة أولاً
                CollectDataFromAllGrids();

                // تحديث البيانات الأساسية من صفوف العرض
                UpdateUnderlyingDataFromDisplayRows();

                // طباعة تفاصيل البيانات قبل الحفظ
                System.Diagnostics.Debug.WriteLine($"📊 البيانات المراد حفظها:");
                System.Diagnostics.Debug.WriteLine($"   - عدد المؤشرات: {ipttData?.Count ?? 0}");
                System.Diagnostics.Debug.WriteLine($"   - عدد الأشهر: {monthColumns?.Count ?? 0}");
                System.Diagnostics.Debug.WriteLine($"   - عدد المواقع: {locationData?.Count ?? 0}");

                // طباعة تفاصيل البيانات الشهرية
                if (locationData != null)
                {
                    foreach (var location in locationData)
                    {
                        var dataRowsWithValues = location.Value.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).Count();
                        System.Diagnostics.Debug.WriteLine($"   - الموقع {location.Key}: {location.Value.Count} صف، {dataRowsWithValues} صف يحتوي على بيانات");
                    }
                }

                // حفظ في قاعدة البيانات باستخدام الطريقة الصحيحة
                bool success = await databaseService.SaveIpttDataAsync(
                    currentProject,
                    ipttData ?? new ObservableCollection<IpttIndicator>(),
                    monthColumns ?? new List<string>(),
                    locationData ?? new Dictionary<int, ObservableCollection<IpttDisplayRow>>()
                );

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم الحفظ التلقائي بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ فشل في الحفظ التلقائي");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحفظ التلقائي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار اتصال قاعدة البيانات
        /// </summary>
        private async void TestDatabaseConnectionAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 اختبار اتصال قاعدة البيانات...");

                // محاولة تحميل بيانات وهمية للتأكد من عمل قاعدة البيانات
                var testResult = await databaseService.LoadIpttDataAsync("test_connection");

                System.Diagnostics.Debug.WriteLine("✅ قاعدة البيانات تعمل بشكل صحيح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ مشكلة في قاعدة البيانات: {ex.Message}");
                MessageBox.Show($"تحذير: قد تكون هناك مشكلة في قاعدة البيانات.\nالتفاصيل: {ex.Message}\n\nسيتم المتابعة لكن قد لا يتم حفظ البيانات.",
                              "تحذير قاعدة البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// فحص البيانات المحملة للتأكد من صحتها
        /// </summary>
        private void ValidateLoadedData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 فحص البيانات المحملة...");

                // فحص المؤشرات
                System.Diagnostics.Debug.WriteLine($"📊 عدد المؤشرات: {ipttData?.Count ?? 0}");
                if (ipttData != null)
                {
                    foreach (var indicator in ipttData)
                    {
                        System.Diagnostics.Debug.WriteLine($"   - {indicator.Indicator}: {indicator.DataTypes?.Count ?? 0} نوع بيانات");
                    }
                }

                // فحص البيانات الشهرية لكل موقع
                System.Diagnostics.Debug.WriteLine($"📍 عدد المواقع: {locationData?.Count ?? 0}");
                if (locationData != null)
                {
                    foreach (var location in locationData)
                    {
                        var dataRowsWithValues = location.Value.Where(r => r.IsDataTypeRow &&
                            r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).ToList();

                        System.Diagnostics.Debug.WriteLine($"   - الموقع {location.Key}: {location.Value.Count} صف، {dataRowsWithValues.Count} صف يحتوي على بيانات");

                        // طباعة عينة من البيانات
                        foreach (var row in dataRowsWithValues.Take(2))
                        {
                            var nonZeroData = row.MonthlyData.Where(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0").Take(3);
                            var sampleData = string.Join(", ", nonZeroData.Select(m => $"{m.Key}={m.Value}"));
                            System.Diagnostics.Debug.WriteLine($"     * {row.Indicator}-{row.DataType}: {sampleData}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ انتهى فحص البيانات المحملة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص البيانات المحملة: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص قاعدة البيانات مباشرة لمشروع محدد
        /// </summary>
        private async void InspectDatabaseForProject(string projectName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 فحص قاعدة البيانات لمشروع: {projectName}");

                // تحميل البيانات من قاعدة البيانات
                var loadResult = await databaseService.LoadIpttDataAsync(projectName);

                if (loadResult != null)
                {
                    System.Diagnostics.Debug.WriteLine("📋 البيانات الموجودة في قاعدة البيانات:");
                    System.Diagnostics.Debug.WriteLine($"   - المؤشرات: {loadResult.Indicators?.Count ?? 0}");
                    System.Diagnostics.Debug.WriteLine($"   - الأعمدة الشهرية: {loadResult.MonthColumns?.Count ?? 0}");
                    System.Diagnostics.Debug.WriteLine($"   - المواقع: {loadResult.LocationData?.Count ?? 0}");
                    System.Diagnostics.Debug.WriteLine($"   - عدد المواقع: {loadResult.LocationCount}");

                    // فحص البيانات الشهرية لكل موقع
                    if (loadResult.LocationData != null)
                    {
                        foreach (var location in loadResult.LocationData)
                        {
                            var dataRowsWithValues = location.Value.Where(r => r.IsDataTypeRow &&
                                r.MonthlyData.Any(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0")).ToList();

                            System.Diagnostics.Debug.WriteLine($"   📍 الموقع {location.Key}:");
                            System.Diagnostics.Debug.WriteLine($"      - إجمالي الصفوف: {location.Value.Count}");
                            System.Diagnostics.Debug.WriteLine($"      - صفوف تحتوي على بيانات: {dataRowsWithValues.Count}");

                            // طباعة البيانات الفعلية
                            foreach (var row in dataRowsWithValues)
                            {
                                var nonZeroData = row.MonthlyData.Where(m => !string.IsNullOrWhiteSpace(m.Value) && m.Value != "0");
                                foreach (var data in nonZeroData)
                                {
                                    System.Diagnostics.Debug.WriteLine($"         * {row.Indicator}-{row.DataType} [{data.Key}]: {data.Value}");
                                }
                            }
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على بيانات في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة - حفظ البيانات قبل الإغلاق
        /// </summary>
        private void IPTTDesignWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 حفظ البيانات قبل إغلاق النافذة...");

                // حفظ البيانات بدون انتظار أو إلغاء الإغلاق
                AutoSaveDataAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ البيانات قبل الإغلاق");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات قبل الإغلاق: {ex.Message}");
            }
        }
    }
}
