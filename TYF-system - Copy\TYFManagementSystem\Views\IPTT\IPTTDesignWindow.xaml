<Window x:Class="TYFManagementSystem.Views.IPTT.IPTTDesignWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تصميم IPTT - جدول المؤشرات والأهداف"
        Height="680"
        Width="1260"
        MinHeight="680"
        MinWidth="1260"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowState="Maximized"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="100"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF9800" CornerRadius="0,0,15,15">
            <Grid>
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <TextBlock Text="📊"
                             FontSize="28"
                             Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock x:Name="ProjectTitleText"
                                  Text="تصميم IPTT - جدول المؤشرات والأهداف"
                                  FontSize="16"
                                  FontWeight="Bold"
                                  Foreground="White"/>
                        <TextBlock x:Name="ProjectNameText"
                                  FontSize="12"
                                  Foreground="White"
                                  Margin="0,3,0,0"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Project Info Card -->
        <Border Grid.Row="1"
                Background="White"
                Margin="15,8,15,39"
                CornerRadius="8"
                Padding="12">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="عنوان المشروع:" FontWeight="Bold" FontSize="11" Foreground="#333"/>
                    <TextBlock x:Name="ProjectTitle" FontSize="10" Margin="0,1,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="مدير المشروع:" FontWeight="Bold" FontSize="11" Foreground="#333"/>
                    <TextBlock x:Name="ProjectManager" FontSize="10" Margin="0,1,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="مدة المشروع:" FontWeight="Bold" FontSize="11" Foreground="#333"/>
                    <TextBlock x:Name="ProjectDuration" FontSize="10" Margin="0,1,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="3">
                    <TextBlock Text="مواقع المشروع:" FontWeight="Bold" FontSize="11" Foreground="#333"/>
                    <TextBlock x:Name="ProjectLocations" FontSize="10" Margin="0,1,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="4" Margin="0,0,0,-12">
                    <TextBlock Text="اختيار الموقع:" FontWeight="Bold" FontSize="11" Foreground="#333" Width="80" HorizontalAlignment="Left"/>
                    <StackPanel Orientation="Horizontal" Margin="0,1,0,0" Height="23">
                        <ComboBox x:Name="LocationSelectionComboBox"
                                 Width="120"
                                 Height="20"
                                 FontSize="10"
                                 HorizontalContentAlignment="Center"
                                 VerticalContentAlignment="Center"
                                 BorderBrush="#ddd"
                                 BorderThickness="1"
                                 Padding="2"
                                 SelectionChanged="LocationSelectionComboBox_SelectionChanged"
                                 IsEditable="False"
                                 Background="White"/>
                        <TextBox x:Name="LocationCountTextBox"
                                Width="40"
                                Height="20"
                                FontSize="10"
                                Text="1"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                BorderBrush="#ddd"
                                BorderThickness="1"
                                Padding="2"
                                Margin="5,0,0,0"
                                ToolTip="عدد المواقع"
                                TextChanged="LocationCountTextBox_TextChanged"/>
                        <Button x:Name="ApplyLocationCountButton"
                               Content="تطبيق"
                               Width="45"
                               Height="20"
                               FontSize="9"
                               Background="#4CAF50"
                               Foreground="White"
                               BorderThickness="0"
                               Cursor="Hand"
                               Margin="3,0,0,0"
                               ToolTip="أدخل المؤشرات أولاً، ثم حدد عدد المواقع واضغط تطبيق"
                               Click="ApplyLocationCountButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- IPTT Table with Location Selection -->
        <Border Grid.Row="1"
                Background="White"
                Margin="15,74,15,8"
                CornerRadius="8"
                Padding="10" Grid.RowSpan="2">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Location Info Header -->
                <Border Grid.Row="0"
                        Background="#f8f9fa"
                        CornerRadius="8,8,0,0"
                        Padding="15,10"
                        Margin="0,0,0,5"
                        BorderBrush="#ddd"
                        BorderThickness="1,1,1,0">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="📍" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock x:Name="CurrentLocationText"
                                  Text="الموقع الحالي: الجدول الرئيسي"
                                  FontSize="14"
                                  FontWeight="Bold"
                                  Foreground="#4A90E2"/>
                        <TextBlock x:Name="LocationDataStatusText"
                                  Text=""
                                  FontSize="12"
                                  Foreground="#666"
                                  Margin="15,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Data Grid -->
                <ScrollViewer Grid.Row="1"
                             HorizontalScrollBarVisibility="Auto"
                             VerticalScrollBarVisibility="Auto">
                    <DataGrid x:Name="IpttDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="False"
                             CanUserResizeColumns="True"
                             CanUserResizeRows="False"
                             CanUserSortColumns="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             SelectionUnit="Cell"
                             Background="White"
                             RowBackground="White"
                             AlternatingRowBackground="#F8F9FA"
                             BorderBrush="Transparent"
                             BorderThickness="0"
                             FontSize="11"
                             MinRowHeight="35"
                             FontFamily="Segoe UI"
                             HorizontalGridLinesBrush="#E5E5E5"
                             VerticalGridLinesBrush="Transparent">

                        <!-- Columns will be generated dynamically in code-behind -->
                    </DataGrid>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- Footer Buttons -->
        <Border Grid.Row="3"
                Background="White"
                BorderBrush="#e0e0e0"
                BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center">

                <Button x:Name="SaveIpttButton"
                        Content="حفظ IPTT"
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="SaveIpttButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="💾"
                                             FontSize="20"
                                             Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45a049"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#3d8b40"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button x:Name="ExportExcelButton"
                        Content="تصدير إلى Excel"
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#2196F3"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="ExportExcelButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="📊"
                                             FontSize="20"
                                             Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#1565C0"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button x:Name="AddIndicatorButton"
                        Content="إضافة مؤشر"
                        Width="110"
                        Height="35"
                        Margin="8"
                        Background="#9C27B0"
                        Foreground="White"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="AddIndicatorButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="➕"
                                             FontSize="16"
                                             Margin="0,0,6,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#7B1FA2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#6A1B9A"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button x:Name="RefreshButton"
                        Content="تحديث"
                        Width="110"
                        Height="35"
                        Margin="8"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="RefreshButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="🔄"
                                             FontSize="16"
                                             Margin="0,0,6,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#66BB6A"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#388E3C"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <!-- زر حفظ البيانات لجميع المواقع - يظهر فقط عند وجود أكثر من موقع واحد -->
                <Button x:Name="SaveAllLocationsButton"
                        Content="حفظ البيانات لجميع المواقع"
                        Width="200"
                        Height="35"
                        Margin="8"
                        Background="#2196F3"
                        Foreground="White"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Visibility="Collapsed"
                        ToolTip="حفظ بيانات جميع المواقع في قاعدة البيانات"
                        Click="SaveAllLocationsButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="💾"
                                             FontSize="16"
                                             Margin="0,0,6,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#42A5F5"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <!-- زر فحص البيانات - مؤقت للتشخيص -->
                <Button x:Name="CheckDataButton"
                        Content="فحص البيانات"
                        Width="120"
                        Height="35"
                        Margin="8"
                        Background="#FF5722"
                        Foreground="White"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        ToolTip="فحص البيانات في قاعدة البيانات"
                        Click="CheckDataButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="🔍"
                                             FontSize="16"
                                             Margin="0,0,6,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FF7043"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#D84315"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button x:Name="CloseButton"
                        Content="إغلاق"
                        Width="110"
                        Height="35"
                        Margin="8"
                        Background="#f44336"
                        Foreground="White"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="CloseButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <TextBlock Text="❌"
                                             FontSize="16"
                                             Margin="0,0,6,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#da190b"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#c62828"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
